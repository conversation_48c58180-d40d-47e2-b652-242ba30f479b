import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_recruit():
    """执行招募任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行招募任务...")
        
        # 使用通用滑动查找函数查找招募功能
        if not utils.swipe_and_find_template("tpl1744707569426.png"):
            print("  -> [错误] 未找到招募功能")
            return False
        
        # 等待招募界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744707593130.png")
        print("  -> 招募界面已加载")
        
        # 检查是否有免费招募
        current_screen = screen.get_screenshot()
        if vision.find_template(current_screen, "tpl1745032336513.png"):
            print("  -> 发现免费招募，执行招募...")
            utils.wait_and_tap_template("tpl1745032355871.png")
            print("  -> 已点击免费招募")
            time.sleep(20)  # 等待招募动画
            
            # 等待招募结果（只等待，不点击）
            utils.wait_for_template("tpl1744707732151.png")
            print("  -> 招募结果已显示")
            
            # 点击确认按钮
            utils.wait_and_tap_template("tpl1744707743818.png")
            print("  -> 已确认招募结果")
        
        # 等待返回招募列表界面（只等待，不点击）
        utils.wait_for_template("tpl1745032433246.png")
        print("  -> 返回招募列表")
        
        # 点击普通招募按钮
        utils.wait_and_tap_template("tpl1744707600962.png")
        print("  -> 点击普通招募")
        
 
        utils.wait_for_template("tpl1744707618634.png")
        print("  -> 确认次数")
        

        utils.wait_and_tap_template("tpl1744707618634.png")
        print("  -> 确认点击")
        
        time.sleep(20)  # 等待返回动画
        
        # 等待返回结果界面（只等待，不点击）
        utils.wait_for_template("tpl1744707732151.png")
        print("  -> 返回结果已显示")
        
        # 点击确认
        utils.wait_and_tap_template("tpl1744707743818.png")
        print("  -> 确认返回结果")
        
        # 等待最终确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744707783978.png")
        print("  -> 等待最终确认")
        
        # 点击最终确认
        utils.wait_and_tap_template("tpl1744707797755.png")
        print("  -> 最终确认完成")
        
        print("  -> 招募任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc() 