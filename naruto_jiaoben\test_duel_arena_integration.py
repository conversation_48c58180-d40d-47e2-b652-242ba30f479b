#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试决斗场 MuMu 宏录制集成
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否成功"""
    try:
        from naruto_automator.operations import duel_arena
        print("✅ 成功导入 duel_arena 模块")
        
        # 测试新增的函数是否存在
        functions_to_test = [
            'run_duel_arena_mumu_macro',
            'run_mumu_macro',
            'run_mumu_macro_quick',
            'run_mumu_macro_test',
            '_collect_duel_arena_rewards'
        ]
        
        for func_name in functions_to_test:
            if hasattr(duel_arena, func_name):
                print(f"✅ 函数 {func_name} 存在")
            else:
                print(f"❌ 函数 {func_name} 不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_mumu_controller_import():
    """测试 MuMu 控制器导入"""
    try:
        from mumu_macro_flow_controller import MuMuMacroFlowController
        print("✅ 成功导入 MuMuMacroFlowController")
        
        # 创建实例测试
        controller = MuMuMacroFlowController()
        print("✅ 成功创建 MuMuMacroFlowController 实例")
        
        return True
        
    except ImportError as e:
        print(f"❌ MuMu 控制器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ MuMu 控制器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("决斗场 MuMu 宏录制集成测试")
    print("=" * 50)
    
    # 测试导入
    print("\n1. 测试模块导入...")
    if not test_import():
        return False
    
    print("\n2. 测试 MuMu 控制器导入...")
    if not test_mumu_controller_import():
        return False
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！集成成功！")
    print("=" * 50)
    
    print("\n可用的决斗场函数:")
    print("- run()                    # 标准模式（40分钟）")
    print("- run_mumu_macro()         # MuMu 宏录制模式（40分钟）")
    print("- run_mumu_macro_quick()   # MuMu 宏录制快速模式（20分钟）")
    print("- run_mumu_macro_test()    # MuMu 宏录制测试模式（5分钟）")
    print("- run_traditional()        # 传统模式（30分钟）")
    print("- run_quick()              # 快速模式（20分钟）")
    
    return True

if __name__ == "__main__":
    main()
