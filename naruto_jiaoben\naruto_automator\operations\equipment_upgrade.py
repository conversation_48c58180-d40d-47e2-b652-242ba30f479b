import time
import random
from core import controller, screen, vision
from naruto_automator.states import state_checker
from naruto_automator import utils

def run():
    """
    执行"升级装备"任务的主函数。
    """
    print("  -> 开始'升级装备'任务流程")

    # 任务开始前：检查是否在主菜单
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return

    try:
        _enter_equipment_menu()

        # 最多循环10次，对应你旧脚本的 max_attempts
        for attempt in range(10):
            print(f"\n  -> 开始第 {attempt + 1}/10 次装备查找与升级尝试...")
            
            # 查找并点击一个可升级的紫色装备
            # 如果没有找到，这个函数会返回 False，然后我们可以中断循环
            if not _find_and_click_purple_equipment():
                print("  -> 未找到可升级的紫色装备，结束升级任务。")
                break # 跳出 for 循环
            
            # 点击后，处理后续的升级流程
            # 如果升级成功并返回，这个函数会返回 True
            if _handle_upgrade_process():
                print("  -> 一轮升级流程成功完成。")
                break # 升级成功，跳出 for 循环
            else:
                print("  -> 本轮升级尝试后未成功（或已处理完），继续下一次尝试...")
                # 这里可以加一个返回操作，确保回到装备列表界面
                # controller.tap(x, y) # 点击返回按钮

        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()

    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

def _enter_equipment_menu():
    """进入装备菜单"""
    print("  -> 进入装备菜单...")
    # 对应 tpl1745056969032.png，从主菜单点击"装备"
    utils.wait_and_tap_template("tpl1745056969032.png")
    # 对应 tpl1745057005807.png，可能是等待"装备"界面的某个标志
    utils.wait_for_template("tpl1745057005807.png")
    # 对应 tpl1745057016806.png，可能是点击"全部装备"之类的筛选按钮
    utils.wait_and_tap_template("tpl1745057016806.png")

def _find_and_click_purple_equipment():
    target_slot = {"region": (184, 257, 34, 23), "pos": [202, 269]}
    print("  -> [占位符] 颜色检测功能需要你从旧脚本迁移，此处假设装备是紫色。")
    target_position = target_slot["pos"]
    print(f"  -> 点击指定装备位置: {target_position}")
    controller.tap(target_position[0], target_position[1])
    time.sleep(2)
    return True

def _handle_upgrade_process():

    try:
        # 对应 tpl1745558097285.png，点击"升级"
        utils.wait_and_tap_template("tpl1745558097285.png")
        # 对应 tpl1745558962639.png，点击"自动选择"
        utils.wait_and_tap_template("tpl1745558962639.png")
        # 对应 tpl1745558973352.png，点击"确认选择"
        utils.wait_and_tap_template("tpl1745558973352.png")
        # 对应 tpl1745559136680.png, 等待材料选择后的界面
        utils.wait_for_template("tpl1745559136680.png")
        # 对应 tpl1745559144992.png，点击"升级"确认
        utils.wait_and_tap_template("tpl1745559144992.png")

        # 处理后续可能的分支
        time.sleep(20) # 等待一下，看会出现哪个弹窗
        screen_shot = screen.get_screenshot()
        

        if vision.find_template(screen_shot, "tpl1745561229286.png"):
            print("  -> 体力不足...")
            utils.wait_and_tap_template("tpl1745561242717.png") 
            utils.wait_and_tap_template("tpl1745560152018.png") 
            time.sleep(5)
            utils.wait_and_tap_template("tpl1745560176571.png") 
            time.sleep(5)
            utils.wait_and_tap_template("tpl1745561299052.png") 
            print("  -> 强制结束...")
            return True 

        elif vision.find_template(screen_shot, "tpl1745560245746.png"):
            print("  -> 收集完单个材料...")
            utils.wait_and_tap_template("tpl1745560152018.png") # 点击关闭
            print("  -> 重复步骤...")

            utils.wait_and_tap_template("tpl1745560176571.png") 
            time.sleep(5)
            return False 
        else:
            print("  -> 未检测到特殊弹窗，可能是一次普通升级。")
            time.sleep(10) # 等待普通升级动画
            # 尝试点击返回
            utils.wait_and_tap_template("tpl1745560152018.png")
            print("  -> 重复步骤...")

            utils.wait_and_tap_template("tpl1745560176571.png") 
            time.sleep(5)
            return False # 

    except TimeoutError:
        print("  -> 在升级流程中发生超时，可能装备无法升级或材料不足。返回装备列表。")
        # 尝试通过通用返回按钮回到装备列表
        utils.wait_and_tap_template("tpl1721210501833.png")
        return False 