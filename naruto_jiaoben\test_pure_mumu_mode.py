#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试纯 MuMu 宏录制模式
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pure_mumu_mode():
    """测试纯 MuMu 宏录制模式"""
    try:
        from naruto_automator.operations import duel_arena
        from mumu_macro_flow_controller import MuMuMacroFlowController
        
        print("=" * 60)
        print("测试纯 MuMu 宏录制模式")
        print("=" * 60)
        
        # 测试 MuMu 控制器
        print("\n1. 测试 MuMu 控制器初始化...")
        controller = MuMuMacroFlowController()
        
        if controller.mumu_main_window:
            print("✅ 找到 MuMu 模拟器窗口")
            print(f"   窗口句柄: {controller.mumu_main_window}")
        else:
            print("❌ 未找到 MuMu 模拟器窗口")
            print("   这意味着决斗场任务将会失败")
        
        # 测试决斗场函数
        print("\n2. 测试决斗场函数可用性...")
        functions = [
            ('run', '标准模式（40分钟）'),
            ('run_mumu_macro', 'MuMu 宏录制模式（40分钟）'),
            ('run_mumu_macro_quick', 'MuMu 宏录制快速模式（20分钟）'),
            ('run_mumu_macro_test', 'MuMu 宏录制测试模式（5分钟）'),
            ('run_traditional', '30分钟模式'),
            ('run_quick', '20分钟模式'),
        ]
        
        for func_name, description in functions:
            if hasattr(duel_arena, func_name):
                print(f"✅ {func_name:<25} - {description}")
            else:
                print(f"❌ {func_name:<25} - 函数不存在")
        
        # 模拟测试决斗场逻辑（不实际执行）
        print("\n3. 模拟测试决斗场逻辑...")
        
        if controller.mumu_main_window:
            print("✅ 模拟测试通过:")
            print("   - 会进入决斗场")
            print("   - 会执行 MuMu 宏录制流程")
            print("   - 会领取奖励")
            print("   - 任务成功完成")
        else:
            print("❌ 模拟测试结果:")
            print("   - 会进入决斗场")
            print("   - 检测不到 MuMu 窗口")
            print("   - 直接返回失败，不执行任何点击")
            print("   - 任务失败")
        
        print("\n" + "=" * 60)
        
        if controller.mumu_main_window:
            print("✅ 纯 MuMu 宏录制模式测试通过！")
            print("   您可以正常使用决斗场功能")
        else:
            print("⚠️  纯 MuMu 宏录制模式测试警告！")
            print("   请确保 MuMu 模拟器已启动且窗口标题为 'MuMu模拟器12-1'")
            print("   否则决斗场任务将无法执行")
        
        print("=" * 60)
        
        return controller.mumu_main_window is not None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("使用示例")
    print("=" * 60)
    
    print("\n1. 直接调用（推荐）:")
    print("```python")
    print("from naruto_automator.operations import duel_arena")
    print("")
    print("# MuMu 宏录制模式（40分钟）")
    print("result = duel_arena.run_mumu_macro()")
    print("if result:")
    print("    print('决斗场任务完成！')")
    print("else:")
    print("    print('决斗场任务失败！请检查 MuMu 模拟器')")
    print("```")
    
    print("\n2. 标准接口:")
    print("```python")
    print("# 标准模式（40分钟 MuMu 宏录制）")
    print("duel_arena.run()")
    print("")
    print("# 快速模式（20分钟 MuMu 宏录制）")
    print("duel_arena.run_quick()")
    print("")
    print("# 测试模式（5分钟 MuMu 宏录制）")
    print("duel_arena.run_mumu_macro_test()")
    print("```")
    
    print("\n3. 通过主程序:")
    print("```bash")
    print("# 在 config.yaml 中启用决斗场")
    print("# tasks:")
    print("#   duel_arena:")
    print("#     enabled: true")
    print("")
    print("python main.py")
    print("```")
    
    print("\n4. 通过 UI 界面:")
    print("```bash")
    print("python run_ui.py")
    print("# 在界面中选择决斗场任务")
    print("```")

def main():
    """主函数"""
    success = test_pure_mumu_mode()
    show_usage_examples()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 恭喜！您的环境已准备就绪，可以使用纯 MuMu 宏录制模式！")
    else:
        print("⚠️  请先启动 MuMu 模拟器，然后重新运行此测试")
    print("=" * 60)

if __name__ == "__main__":
    main()
