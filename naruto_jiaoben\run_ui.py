#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火影忍者自动化脚本 - UI启动器
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动UI"""
    try:
        from ui.main_window import main
        main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        input("按回车键退出...")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main() 