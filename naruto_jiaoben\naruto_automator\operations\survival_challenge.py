import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_survival_challenge():
    """执行生存挑战任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行生存挑战任务...")
        
        # 使用通用滑动查找函数查找生存挑战功能
        if not utils.swipe_and_find_template("tpl1744708340134.png"):
            print("  -> [错误] 未找到生存挑战功能")
            return False
        controller.tap(606, 317)
        controller.tap(606, 317)
        print("  -> 已点击指定位置")
        # 等待生存挑战界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744708426147.png")
        print("  -> 生存挑战界面已加载")
        
        # 点击指定位置两次
        controller.tap(606, 317)
        controller.tap(606, 317)
        print("  -> 已点击指定位置")
        
        time.sleep(5)  # 等待界面响应
        
        # 点击开始挑战按钮
        utils.wait_and_tap_template("tpl1744773918104.png")
        print("  -> 已点击开始挑战")
        
        # 等待挑战确认界面（只等待，不点击）
        time.sleep(10)

        # 点击确认开始
        # 如果检测到确认按钮则点击，否则继续（不等待）
        if utils.tap_template("tpl1744773858903.png"):
            print("  -> 已确认开始挑战")
        else:
            print("  -> 未检测到确认按钮，继续执行")
        
        time.sleep(5)  # 等待挑战开始
        
        # 点击指定位置
        controller.tap(510, 631)
        print("  -> 已点击指定位置")
        
        # 等待挑战界面重新加载（只等待，不点击）
        utils.wait_for_template("tpl1744708426147.png")
        print("  -> 挑战界面重新加载")
        
        # 点击挑战按钮
        utils.wait_and_tap_template("tpl1744774096960.png")
        print("  -> 已点击挑战按钮")
        
        # 等待挑战开始界面（只等待，不点击）
        utils.wait_for_template("tpl1744708959155.png")
        print("  -> 挑战开始界面已加载")
        
        # 点击开始按钮
        utils.wait_and_tap_template("tpl1744708970666.png")
        print("  -> 已点击开始按钮")
        
        # 等待挑战进行界面（只等待，不点击）
        utils.wait_for_template("tpl1744709008411.png")
        print("  -> 挑战进行中...")
        
        # 点击继续按钮
        utils.wait_and_tap_template("tpl1744709023483.png")
        print("  -> 已点击继续按钮")
        
        # 等待挑战继续界面（只等待，不点击）
        utils.wait_for_template("tpl1744709008411.png")
        print("  -> 挑战继续中...")
        
        # 再次点击继续按钮
        utils.wait_and_tap_template("tpl1744709023483.png")
        print("  -> 已点击继续按钮")
        
        time.sleep(60.0)  # 等待挑战完成
        
        # 等待挑战完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744709179107.png")
        print("  -> 挑战完成界面已加载")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744709185587.png")
        print("  -> 已点击完成按钮")
        
        print("  -> 生存挑战任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc() 