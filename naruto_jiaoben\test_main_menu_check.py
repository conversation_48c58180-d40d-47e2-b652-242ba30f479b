#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试决斗场主菜单检查逻辑
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_menu_check_logic():
    """测试主菜单检查逻辑"""
    try:
        from naruto_automator.operations import duel_arena
        from naruto_automator.states import state_checker
        from naruto_automator import utils
        
        print("=" * 60)
        print("测试决斗场主菜单检查逻辑")
        print("=" * 60)
        
        # 测试状态检查函数是否可用
        print("\n1. 测试状态检查函数...")
        try:
            is_in_main = state_checker.is_in_main_menu()
            print(f"✅ state_checker.is_in_main_menu() 可用，当前状态: {is_in_main}")
        except Exception as e:
            print(f"❌ state_checker.is_in_main_menu() 调用失败: {e}")
            return False
        
        # 测试恢复函数是否可用
        print("\n2. 测试恢复函数...")
        try:
            # 只测试函数是否存在，不实际调用
            if hasattr(utils, 'handle_timeout_and_return_to_main'):
                print("✅ utils.handle_timeout_and_return_to_main() 函数存在")
            else:
                print("❌ utils.handle_timeout_and_return_to_main() 函数不存在")
                return False
        except Exception as e:
            print(f"❌ 恢复函数测试失败: {e}")
            return False
        
        # 测试决斗场函数中的主菜单检查
        print("\n3. 测试决斗场函数中的主菜单检查...")
        
        # 检查 _collect_duel_arena_rewards 函数
        if hasattr(duel_arena, '_collect_duel_arena_rewards'):
            print("✅ _collect_duel_arena_rewards 函数存在")
        else:
            print("❌ _collect_duel_arena_rewards 函数不存在")
            return False
        
        # 检查主要的决斗场函数
        functions_to_check = [
            'run_duel_arena_mumu_macro',
            'run_duel_arena_simple',
            'run',
            'run_mumu_macro'
        ]
        
        for func_name in functions_to_check:
            if hasattr(duel_arena, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_main_menu_check_flow():
    """显示主菜单检查流程"""
    print("\n" + "=" * 60)
    print("主菜单检查流程说明")
    print("=" * 60)
    
    print("\n🔄 完整流程:")
    print("1. 任务开始前检查")
    print("   ├── 检查是否在主菜单")
    print("   ├── 如果不在 → 尝试恢复到主菜单")
    print("   └── 恢复失败 → 任务中断")
    print()
    print("2. 执行决斗场任务")
    print("   ├── 进入决斗场")
    print("   ├── MuMu 宏录制流程")
    print("   └── 领取奖励")
    print()
    print("3. 任务结束后检查（修复后）")
    print("   ├── 正常完成 → 检查主菜单 → 恢复（如需要）")
    print("   ├── 超时异常 → 检查主菜单 → 恢复（如需要）")
    print("   └── 其他异常 → 检查主菜单 → 恢复（如需要）")
    
    print("\n🔧 修复内容:")
    print("- 在 TimeoutError 异常处理中添加主菜单检查")
    print("- 在 Exception 异常处理中添加主菜单检查")
    print("- 确保无论何种情况都会尝试返回主菜单")
    
    print("\n⚠️ 问题原因:")
    print("- 原来：超时异常 → 直接返回 False → 跳过主菜单检查")
    print("- 现在：超时异常 → 检查主菜单 → 尝试恢复 → 返回结果")

def show_code_changes():
    """显示代码修改内容"""
    print("\n" + "=" * 60)
    print("代码修改内容")
    print("=" * 60)
    
    print("\n📝 修改前:")
    print("```python")
    print("except TimeoutError:")
    print("    print('  -> [警告] 奖励领取超时，可能已经领取过或界面异常')")
    print("    return False  # 直接返回，跳过主菜单检查")
    print("```")
    
    print("\n📝 修改后:")
    print("```python")
    print("except TimeoutError:")
    print("    print('  -> [警告] 奖励领取超时，可能已经领取过或界面异常')")
    print("    # 即使超时也要检查主菜单状态")
    print("    print('  -> 检查当前是否在主菜单...')")
    print("    if state_checker.is_in_main_menu():")
    print("        print('  -> 确认已成功返回主菜单。')")
    print("        return True  # 虽然超时但已在主菜单，认为任务成功")
    print("    else:")
    print("        print('  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...')")
    print("        utils.handle_timeout_and_return_to_main()")
    print("        return False")
    print("```")

def main():
    """主函数"""
    print("决斗场主菜单检查逻辑测试")
    
    # 测试主菜单检查逻辑
    success = test_main_menu_check_logic()
    
    # 显示流程说明
    show_main_menu_check_flow()
    
    # 显示代码修改
    show_code_changes()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 主菜单检查逻辑测试通过！")
        print("   现在即使奖励领取超时，也会检查并尝试返回主菜单")
    else:
        print("❌ 主菜单检查逻辑测试失败！")
    print("=" * 60)
    
    print("\n💡 使用建议:")
    print("- 如果经常遇到奖励领取超时，可以增加超时时间")
    print("- 检查 tpl1744778739345.png 模板图片是否准确")
    print("- 确保网络连接稳定，避免界面加载缓慢")

if __name__ == "__main__":
    main()
