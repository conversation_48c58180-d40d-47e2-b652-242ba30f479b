#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MuMu宏录制开始控制器
只包含宏录制的开始部分（步骤1-9）
"""

import time
import win32gui
import win32con
import win32api
from pathlib import Path

class MuMuMacroStartController:
    """MuMu宏录制开始控制器"""
    
    def __init__(self):
        self.mumu_main_window = None
        self.mumu_record_window = None
        self.find_windows()
    
    def find_windows(self):
        """查找MuMu相关窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        print("查找MuMu相关窗口...")
        
        for hwnd, title in windows:
            if title == "MuMu模拟器12-1":
                self.mumu_main_window = hwnd
                print(f"✅ 找到主窗口: {title}")
            elif "操作录制" in title or "MuMu 操作录制" in title:
                self.mumu_record_window = hwnd
                print(f"✅ 找到录制窗口: {title}")
        
        if not self.mumu_main_window:
            print("❌ 未找到MuMu模拟器12-1窗口")
        
        return self.mumu_main_window is not None
    
    def bring_window_to_front(self, hwnd, window_name):
        """将指定窗口置顶"""
        try:
            if not win32gui.IsWindow(hwnd):
                print(f"❌ {window_name} 窗口句柄无效")
                return False
            
            print(f"  -> 将 {window_name} 窗口置顶...")
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.5)
            
            print(f"✅ {window_name} 窗口已置顶")
            return True
            
        except Exception as e:
            print(f"❌ 置顶 {window_name} 窗口失败: {e}")
            return False
    
    def send_window_to_background(self, hwnd, window_name):
        """将指定窗口放到后台"""
        try:
            print(f"  -> 将 {window_name} 窗口放到后台...")
            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
            time.sleep(0.3)
            
            print(f"✅ {window_name} 窗口已放到后台")
            return True
            
        except Exception as e:
            print(f"❌ 放置 {window_name} 窗口到后台失败: {e}")
            return False
    
    def send_alt_p_hotkey(self):
        """发送Alt+P快捷键"""
        try:
            print("  -> 发送Alt+P快捷键...")
            
            VK_ALT = 0x12
            VK_P = ord('P')
            
            # 全局按键发送
            win32api.keybd_event(VK_ALT, 0, 0, 0)  # Alt按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, 0, 0)    # P按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, win32con.KEYEVENTF_KEYUP, 0)    # P释放
            time.sleep(0.05)
            win32api.keybd_event(VK_ALT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
            
            print("✅ Alt+P快捷键已发送")
            return True
            
        except Exception as e:
            print(f"❌ 发送Alt+P失败: {e}")
            return False
    
    def wait_for_record_window(self, timeout=10):
        """等待操作录制窗口出现"""
        print(f"  -> 等待操作录制窗口出现 (超时: {timeout}秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 重新查找窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if "操作录制" in window_title or "MuMu 操作录制" in window_title:
                        windows.append((hwnd, window_title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                self.mumu_record_window = windows[0][0]
                print(f"✅ 找到操作录制窗口: {windows[0][1]}")
                return True
            
            time.sleep(0.5)
        
        print("❌ 等待操作录制窗口超时")
        return False
    
    def click_image_template(self, template_path, threshold=0.8):
        """点击图像模板"""
        try:
            import pyautogui
            
            if not Path(template_path).exists():
                print(f"❌ 模板图片不存在: {template_path}")
                return False
            
            print(f"  -> 查找并点击图像: {template_path}")
            
            # 使用pyautogui查找图像
            location = pyautogui.locateOnScreen(template_path, confidence=threshold)
            
            if location:
                # 计算中心点
                center_x = location.left + location.width // 2
                center_y = location.top + location.height // 2
                
                # 点击
                pyautogui.click(center_x, center_y)
                print(f"✅ 已点击图像位置: ({center_x}, {center_y})")
                return True
            else:
                print(f"❌ 未找到图像: {template_path}")
                return False
                
        except Exception as e:
            print(f"❌ 点击图像失败: {e}")
            return False

    def click_fixed_position(self, x, y):
        """点击固定坐标位置"""
        try:
            import pyautogui

            print(f"  -> 点击固定坐标: ({x}, {y})")
            pyautogui.click(x, y)
            print(f"✅ 已点击坐标: ({x}, {y})")
            return True

        except Exception as e:
            print(f"❌ 点击固定坐标失败: {e}")
            return False
    
    def execute_start_flow(self):
        """执行宏录制开始流程（步骤1-9）"""
        print("开始执行MuMu宏录制开始流程")
        print("=" * 50)
        
        try:
            # 步骤1：调出MuMu模拟器窗口并置顶
            print("\n步骤1: 调出MuMu模拟器窗口并置顶")
            if not self.bring_window_to_front(self.mumu_main_window, "MuMu模拟器12-1"):
                return False
            
            # 步骤2：发送Alt+P快捷键
            print("\n步骤2: 发送Alt+P快捷键")
            if not self.send_alt_p_hotkey():
                return False
            
            # 步骤3：将MuMu窗口放到后台
            print("\n步骤3: 将MuMu窗口放到后台")
            if not self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1"):
                return False
            
            # 步骤4：等待操作录制窗口出现
            print("\n步骤4: 等待操作录制窗口出现")
            if not self.wait_for_record_window(timeout=5):
                print("❌ 操作录制窗口未出现，可能快捷键无效")
                return False
            
            # 步骤5：将操作录制窗口置顶
            print("\n步骤5: 将操作录制窗口置顶")
            if not self.bring_window_to_front(self.mumu_record_window, "MuMu 操作录制"):
                return False
            
            # 步骤6：自动点击第一个图标
            print("\n步骤6: 自动点击第一个图标")
            template1_path = "record_button1.png"

            if Path(template1_path).exists():
                print(f"  -> 使用模板图片: {template1_path}")
                if self.click_image_template(template1_path):
                    print("✅ 第一个图标点击成功")
                    time.sleep(2)  # 等待界面响应
                else:
                    print(f"❌ 点击第一个图标失败，尝试备用坐标")
                    # 备用方案：使用固定坐标点击
                    self.click_fixed_position(200, 100)  # 需要根据实际情况调整
                    time.sleep(2)
            else:
                print(f"⚠️ 模板图片不存在: {template1_path}，使用备用坐标")
                # 备用方案：使用固定坐标
                self.click_fixed_position(200, 100)  # 需要根据实际情况调整
                time.sleep(2)

            # 步骤7：自动点击第二个图标
            print("\n步骤7: 自动点击第二个图标")
            template2_path = "record_button2.png"

            if Path(template2_path).exists():
                print(f"  -> 使用模板图片: {template2_path}")
                if self.click_image_template(template2_path):
                    print("✅ 第二个图标点击成功")
                    time.sleep(2)  # 等待界面响应
                else:
                    print(f"❌ 点击第二个图标失败，尝试备用坐标")
                    # 备用方案：使用固定坐标点击
                    self.click_fixed_position(300, 150)  # 需要根据实际情况调整
                    time.sleep(2)
            else:
                print(f"⚠️ 模板图片不存在: {template2_path}，使用备用坐标")
                # 备用方案：使用固定坐标
                self.click_fixed_position(300, 150)  # 需要根据实际情况调整
                time.sleep(2)
            
            # 步骤8：等待模拟器响应
            print("\n步骤8: 等待模拟器响应...")
            time.sleep(3)  # 等待模拟器处理

            # 步骤9：将MuMu主窗口放到后台
            print("\n步骤9: 将MuMu主窗口放到后台")
            if not self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1"):
                print("⚠️ 无法将MuMu窗口放到后台，继续执行...")

            print("\n✅ MuMu宏录制开始流程执行完成！")
            print("📋 已完成步骤:")
            print("  ✅ MuMu窗口管理")
            print("  ✅ Alt+P快捷键发送")
            print("  ✅ 操作录制窗口控制")
            print("  ✅ 第一个按钮点击")
            print("  ✅ 第二个按钮点击")
            print("  ✅ MuMu模拟器已放到后台")
            print("\n🎯 现在可以开始录制您的操作了！")

            return True
            
        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")
            return False

def main():
    """主函数"""
    print("MuMu宏录制开始控制器")
    print("=" * 50)

    controller = MuMuMacroStartController()

    if not controller.mumu_main_window:
        print("❌ 未找到MuMu模拟器窗口，请确保MuMu已启动")
        return

    print("✅ 找到MuMu窗口，开始执行宏录制开始流程...")
    time.sleep(2)  # 给用户2秒准备时间

    if controller.execute_start_flow():
        print("\n🎉 宏录制开始流程执行成功！")
        print("现在您可以在游戏中进行需要录制的操作了。")
    else:
        print("\n❌ 宏录制开始流程执行失败！")

if __name__ == "__main__":
    main()
