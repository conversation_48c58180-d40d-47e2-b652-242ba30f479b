#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图助手 - 帮助创建模板图片
"""

import time
import tkinter as tk
from tkinter import messagebox, filedialog
import pyautogui
from PIL import Image, ImageTk
import threading

class ScreenshotHelper:
    """截图助手"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("截图助手 - 创建模板图片")
        self.root.geometry("600x500")  # 进一步增加窗口大小
        self.root.attributes('-topmost', True)  # 窗口置顶
        self.root.resizable(True, True)  # 允许调整大小

        self.setup_ui()
        
    def setup_ui(self):
        """设置UI界面"""
        # 标题
        title_label = tk.Label(self.root, text="截图助手", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 说明
        info_text = """此工具帮助您创建模板图片：

1. record_button1.png - 第一个按钮（操作录制窗口）
2. record_button2.png - 第二个按钮（操作录制窗口）
3. record_button3.png - 第三个按钮（MuMu模拟器，40分钟后点击）

使用方法：
1. 点击对应的截图按钮
2. 在截图上选择按钮区域
3. 保存为模板图片"""

        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT,
                             font=("Arial", 10), wraplength=550)
        info_label.pack(pady=15, padx=20)
        
        # 按钮区域
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20, fill=tk.BOTH, expand=True, padx=20)

        # 截图按钮 - 垂直排列，增加大小和间距
        screenshot_btn = tk.Button(button_frame, text="📸 截图第一个按钮\n(操作录制窗口)",
                                 command=lambda: self.take_screenshot("record_button1.png"),
                                 bg="lightblue", width=25, height=3,
                                 font=("Arial", 11, "bold"))
        screenshot_btn.pack(pady=8, fill=tk.X)

        screenshot_btn2 = tk.Button(button_frame, text="📸 截图第二个按钮\n(操作录制窗口)",
                                  command=lambda: self.take_screenshot("record_button2.png"),
                                  bg="lightgreen", width=25, height=3,
                                  font=("Arial", 11, "bold"))
        screenshot_btn2.pack(pady=8, fill=tk.X)

        # 第三个截图按钮
        screenshot_btn3 = tk.Button(button_frame, text="📸 截图第三个按钮\n(MuMu模拟器，40分钟后)",
                                  command=lambda: self.take_screenshot("record_button3.png"),
                                  bg="lightcoral", width=25, height=3,
                                  font=("Arial", 11, "bold"))
        screenshot_btn3.pack(pady=8, fill=tk.X)

        # 分隔线
        separator = tk.Frame(button_frame, height=2, bg="gray")
        separator.pack(fill=tk.X, pady=10)

        # 全屏截图按钮
        fullscreen_btn = tk.Button(button_frame, text="📷 全屏截图（用于参考）",
                                 command=self.take_fullscreen_screenshot,
                                 bg="lightyellow", width=25, height=2,
                                 font=("Arial", 10))
        fullscreen_btn.pack(pady=5, fill=tk.X)
        
        # 状态标签
        self.status_label = tk.Label(self.root, text="✅ 就绪", fg="green",
                                   font=("Arial", 10, "bold"))
        self.status_label.pack(pady=10)

        # 退出按钮
        exit_btn = tk.Button(self.root, text="❌ 退出", command=self.root.quit,
                           width=15, height=2, bg="lightgray",
                           font=("Arial", 10))
        exit_btn.pack(pady=10)
    
    def countdown(self, seconds=3):
        """倒计时"""
        for i in range(seconds, 0, -1):
            self.status_label.config(text=f"倒计时: {i} 秒", fg="red")
            self.root.update()
            time.sleep(1)
        
        self.status_label.config(text="截图中...", fg="blue")
        self.root.update()
    
    def take_screenshot(self, filename):
        """截取区域截图"""
        try:
            # 最小化窗口
            self.root.iconify()
            
            # 倒计时
            time.sleep(0.5)  # 等待窗口最小化
            
            # 截取全屏
            screenshot = pyautogui.screenshot()
            
            # 恢复窗口
            self.root.deiconify()
            
            # 创建选择区域窗口
            self.create_selection_window(screenshot, filename)
            
        except Exception as e:
            messagebox.showerror("错误", f"截图失败: {e}")
            self.status_label.config(text="截图失败", fg="red")
    
    def create_selection_window(self, screenshot, filename):
        """创建区域选择窗口"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择按钮区域")
        selection_window.attributes('-topmost', True)
        
        # 缩放截图以适应窗口
        screen_width = screenshot.width
        screen_height = screenshot.height
        
        # 计算缩放比例
        max_width = 800
        max_height = 600
        scale = min(max_width / screen_width, max_height / screen_height)
        
        new_width = int(screen_width * scale)
        new_height = int(screen_height * scale)
        
        resized_screenshot = screenshot.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 转换为Tkinter图像
        photo = ImageTk.PhotoImage(resized_screenshot)
        
        # 创建画布
        canvas = tk.Canvas(selection_window, width=new_width, height=new_height)
        canvas.pack()
        
        # 显示截图
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        canvas.image = photo  # 保持引用
        
        # 选择区域变量
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        
        def on_mouse_down(event):
            self.selection_start = (event.x, event.y)
            if self.selection_rect:
                canvas.delete(self.selection_rect)
        
        def on_mouse_drag(event):
            if self.selection_start:
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
                
                self.selection_rect = canvas.create_rectangle(
                    self.selection_start[0], self.selection_start[1],
                    event.x, event.y,
                    outline="red", width=2
                )
        
        def on_mouse_up(event):
            self.selection_end = (event.x, event.y)
        
        # 绑定鼠标事件
        canvas.bind("<Button-1>", on_mouse_down)
        canvas.bind("<B1-Motion>", on_mouse_drag)
        canvas.bind("<ButtonRelease-1>", on_mouse_up)
        
        # 保存按钮
        def save_selection():
            if self.selection_start and self.selection_end:
                # 计算实际坐标
                x1 = int(min(self.selection_start[0], self.selection_end[0]) / scale)
                y1 = int(min(self.selection_start[1], self.selection_end[1]) / scale)
                x2 = int(max(self.selection_start[0], self.selection_end[0]) / scale)
                y2 = int(max(self.selection_start[1], self.selection_end[1]) / scale)
                
                # 裁剪图像
                cropped = screenshot.crop((x1, y1, x2, y2))
                
                # 保存
                cropped.save(filename)
                
                messagebox.showinfo("成功", f"模板图片已保存: {filename}")
                selection_window.destroy()
                self.status_label.config(text=f"已保存: {filename}", fg="green")
            else:
                messagebox.showwarning("警告", "请先选择区域")
        
        save_btn = tk.Button(selection_window, text="保存选择区域", 
                           command=save_selection, bg="lightgreen")
        save_btn.pack(pady=5)
        
        # 说明
        instruction = tk.Label(selection_window, 
                             text="用鼠标拖拽选择按钮区域，然后点击保存")
        instruction.pack()
    
    def take_fullscreen_screenshot(self):
        """全屏截图用于参考"""
        try:
            # 最小化窗口
            self.root.iconify()
            time.sleep(0.5)
            
            # 截图
            timestamp = int(time.time())
            filename = f"fullscreen_{timestamp}.png"
            
            screenshot = pyautogui.screenshot()
            screenshot.save(filename)
            
            # 恢复窗口
            self.root.deiconify()
            
            messagebox.showinfo("成功", f"全屏截图已保存: {filename}")
            self.status_label.config(text=f"已保存: {filename}", fg="green")
            
        except Exception as e:
            messagebox.showerror("错误", f"截图失败: {e}")
    
    def run(self):
        """运行截图助手"""
        self.root.mainloop()

def main():
    print("启动截图助手...")
    
    try:
        # 检查依赖
        import pyautogui
        from PIL import Image, ImageTk
        
        # 创建并运行截图助手
        helper = ScreenshotHelper()
        helper.run()
        
    except ImportError as e:
        print(f"缺少依赖库: {e}")
        print("请安装: pip install pyautogui pillow")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
