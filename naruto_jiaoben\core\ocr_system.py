import time
import os
from typing import Optional, Tuple, List
from cv2.typing import MatLike
import cv2
import numpy as np

# 设置环境变量解决OpenMP警告
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 尝试导入PaddleOCR
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，OCR功能将不可用")

# 尝试导入EasyOCR作为备选
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

class OCRSystem:
    def __init__(self, ocr_type="paddle"):
        self.ocr_type = ocr_type
        self.ocr_engine = None

    def init(self):
        if self.ocr_type == "paddle":
            self.ocr_engine = PaddleOCR(use_angle_cls=True, lang='ch')
        else:
            raise ValueError("仅支持 paddle OCR")

    def ocr(self, img):
        if self.ocr_type == "paddle":
            return self.ocr_engine.ocr(img, cls=True)
        else:
            raise ValueError("仅支持 paddle OCR")

class NarutoOcrSystem:
    """火影忍者自动化OCR系统"""
    
    def __init__(self, ocr_type: str = "paddle"):
        """
        初始化OCR系统
        
        Args:
            ocr_type: OCR类型，支持 "paddle" 或 "easyocr"
        """
        self.ocr_type = ocr_type
        self.ocr_engine = None
        self._initialized = False
        
    def init_ocr(self, force_reinit: bool = False) -> bool:
        """
        初始化OCR模型
        
        Args:
            force_reinit: 是否强制重新初始化
            
        Returns:
            初始化是否成功
        """
        if self._initialized and not force_reinit:
            return True
            
        try:
            print(f"正在初始化{self.ocr_type.upper()} OCR模型...")
            
            if self.ocr_type == "paddle":
                if not PADDLEOCR_AVAILABLE:
                    print("错误: PaddleOCR未安装，请运行: pip install paddleocr")
                    return False
                # 设置PaddleOCR参数，减少警告
                self.ocr_engine = PaddleOCR(
                    use_angle_cls=True, 
                    lang='ch', 
                    show_log=False,
                    use_gpu=False  # 使用CPU模式避免GPU相关警告
                )
                
            else:
                print(f"不支持的OCR类型: {self.ocr_type}")
                return False
            
            self._initialized = True
            print(f"{self.ocr_type.upper()} OCR模型初始化成功")
            return True
                
        except Exception as e:
            print(f"OCR初始化异常: {e}")
            return False
    
    def ocr_text(self, image: MatLike, region: Optional[Tuple[int, int, int, int]] = None, 
                 threshold: float = 0.5) -> str:
        """
        识别图片中的文字
        
        Args:
            image: 输入图片
            region: 识别区域 (x, y, width, height)
            threshold: 识别阈值
            
        Returns:
            识别到的文字
        """
        if not self._initialized:
            if not self.init_ocr():
                return ""
        
        try:
            # 如果指定了区域，先裁剪图片
            if region is not None:
                x, y, w, h = region
                height, width = image.shape[:2]
                
                # 确保区域在图片范围内
                x = max(0, min(x, width - 1))
                y = max(0, min(y, height - 1))
                w = min(w, width - x)
                h = min(h, height - y)
                
                if w <= 0 or h <= 0:
                    return ""
                
                image = image[y:y+h, x:x+w]
            
            # 执行OCR识别
            if self.ocr_type == "paddle":
                result = self.ocr_engine.ocr(image, cls=True)
                if result and result[0]:
                    # PaddleOCR返回格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
                    texts = [item[1][0] for item in result[0] if item[1][1] >= threshold]
                    return " ".join(texts)
                    
            return ""
            
        except Exception as e:
            print(f"OCR识别异常: {e}")
            return ""
    
    def ocr_to_int(self, image: MatLike, region: Tuple[int, int, int, int], 
                   threshold: float = 0.5) -> Optional[int]:
        """
        识别图片中的数字并转换为整数
        
        Args:
            image: 输入图片
            region: 识别区域 (x, y, width, height)
            threshold: 识别阈值
            
        Returns:
            识别到的整数，失败返回None
        """
        text = self.ocr_text(image, region, threshold)
        if not text:
            return None
        
        try:
            # 提取数字字符
            digits_only = ''.join(c for c in text if c.isdigit())
            if digits_only:
                return int(digits_only)
        except (ValueError, TypeError):
            pass
        
        return None
    
    def find_text_in_image(self, image: MatLike, target_texts: List[str], 
                          threshold: float = 0.5) -> Optional[Tuple[str, int, int]]:
        """
        在图片中查找指定文字
        
        Args:
            image: 输入图片
            target_texts: 目标文字列表
            threshold: 匹配阈值
            
        Returns:
            匹配结果 (文字, x, y)，未找到返回None
        """
        if not self._initialized:
            if not self.init_ocr():
                return None
        
        try:
            # 执行OCR识别
            if self.ocr_type == "paddle":
                result = self.ocr_engine.ocr(image, cls=True)
                if result and result[0]:
                    for item in result[0]:
                        text = item[1][0]
                        confidence = item[1][1]
                        
                        if confidence >= threshold:
                            for target_text in target_texts:
                                if target_text in text:
                                    # 计算中心坐标
                                    points = item[0]
                                    center_x = sum(p[0] for p in points) / 4
                                    center_y = sum(p[1] for p in points) / 4
                                    return (target_text, int(center_x), int(center_y))
                                    
            return None
            
        except Exception as e:
            print(f"文字查找异常: {e}")
            return None
    
    def get_ocr_status(self) -> dict:
        """
        获取OCR系统状态
        
        Returns:
            状态信息字典
        """
        return {
            "ocr_type": self.ocr_type,
            "initialized": self._initialized,
            "engine_loaded": self.ocr_engine is not None,
            "paddleocr_available": PADDLEOCR_AVAILABLE,
            "easyocr_available": EASYOCR_AVAILABLE
        }

# 全局OCR实例
_ocr_system: Optional[NarutoOcrSystem] = None

def get_ocr_system(ocr_type: str = "paddle") -> NarutoOcrSystem:
    """
    获取全局OCR系统实例
    
    Args:
        ocr_type: OCR类型
        
    Returns:
        OCR系统实例
    """
    global _ocr_system
    if _ocr_system is None or _ocr_system.ocr_type != ocr_type:
        _ocr_system = NarutoOcrSystem(ocr_type)
    return _ocr_system

def init_ocr_system(ocr_type: str = "paddle") -> bool:
    """
    初始化全局OCR系统
    
    Args:
        ocr_type: OCR类型
        
    Returns:
        初始化是否成功
    """
    ocr_system = get_ocr_system(ocr_type)
    return ocr_system.init_ocr()

def ocr_text(image: MatLike, region: Optional[Tuple[int, int, int, int]] = None, 
             threshold: float = 0.5) -> str:
    """
    便捷的OCR文字识别函数
    
    Args:
        image: 输入图片
        region: 识别区域
        threshold: 识别阈值
        
    Returns:
        识别到的文字
    """
    ocr_system = get_ocr_system()
    return ocr_system.ocr_text(image, region, threshold)

def ocr_to_int(image: MatLike, region: Tuple[int, int, int, int], 
               threshold: float = 0.5) -> Optional[int]:
    """
    便捷的数字OCR识别函数
    
    Args:
        image: 输入图片
        region: 识别区域
        threshold: 识别阈值
        
    Returns:
        识别到的整数
    """
    ocr_system = get_ocr_system()
    return ocr_system.ocr_to_int(image, region, threshold) 