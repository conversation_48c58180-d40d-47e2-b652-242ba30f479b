#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB配置修复工具
"""

import yaml
import os
from pathlib import Path

def find_mumu_adb():
    """查找MuMu自带的ADB"""
    possible_paths = [
        r"C:\Program Files\Netease\MuMu Player 12\shell\adb.exe",
        r"C:\Program Files (x86)\Netease\MuMu Player 12\shell\adb.exe",
        r"D:\Program Files\Netease\MuMu Player 12\shell\adb.exe",
        r"D:\Program Files (x86)\Netease\MuMu Player 12\shell\adb.exe",
        # MuMu Player 11
        r"C:\Program Files\Netease\MuMu\emulator\nemu\vmonitor\bin\adb_server.exe",
        r"C:\Program Files (x86)\Netease\MuMu\emulator\nemu\vmonitor\bin\adb_server.exe",
    ]
    
    print("查找MuMu自带的ADB...")
    for path in possible_paths:
        if Path(path).exists():
            print(f"✅ 找到MuMu ADB: {path}")
            return path
    
    print("❌ 未找到MuMu自带的ADB")
    return None

def try_common_ports():
    """尝试常见的ADB端口"""
    common_ports = [
        "127.0.0.1:7555",  # MuMu常用端口
        "127.0.0.1:5555",  # 标准ADB端口
        "127.0.0.1:16416", # 当前配置端口
        "127.0.0.1:21503", # 另一个MuMu端口
    ]
    
    print("尝试常见的ADB端口...")
    
    # 使用当前ADB测试端口
    current_adb = r"C:/Users/<USER>/PycharmProjects/jiaoben/platform-tools/adb.exe"
    
    import subprocess
    
    for port in common_ports:
        try:
            print(f"测试端口: {port}")
            
            # 尝试连接
            result = subprocess.run([current_adb, 'connect', port], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and ('connected' in result.stdout or 'already connected' in result.stdout):
                print(f"✅ 端口 {port} 连接成功")
                
                # 测试设备列表
                result = subprocess.run([current_adb, 'devices'], 
                                      capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0 and 'device' in result.stdout:
                    print(f"✅ 端口 {port} 设备可用")
                    return port
                else:
                    print(f"⚠️ 端口 {port} 连接但设备不可用")
            else:
                print(f"❌ 端口 {port} 连接失败")
                
        except Exception as e:
            print(f"❌ 端口 {port} 测试异常: {e}")
    
    return None

def update_config(new_adb_path=None, new_device_uri=None):
    """更新配置文件"""
    config_path = "config.yaml"
    
    try:
        # 读取当前配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"当前配置:")
        print(f"  ADB路径: {config['common']['adb_path']}")
        print(f"  设备地址: {config['common']['device_uri']}")
        
        # 更新配置
        if new_adb_path:
            config['common']['adb_path'] = new_adb_path.replace('\\', '/')
            print(f"✅ 更新ADB路径为: {new_adb_path}")
        
        if new_device_uri:
            config['common']['device_uri'] = new_device_uri
            print(f"✅ 更新设备地址为: {new_device_uri}")
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print("✅ 配置文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")
        return False

def main():
    """主函数"""
    print("ADB配置修复工具")
    print("=" * 50)
    
    # 1. 查找MuMu自带的ADB
    mumu_adb = find_mumu_adb()
    
    # 2. 尝试常见端口
    working_port = try_common_ports()
    
    # 3. 提供修复选项
    print("\n" + "=" * 50)
    print("修复选项:")
    print("=" * 50)
    
    if mumu_adb:
        print(f"1. 使用MuMu自带的ADB: {mumu_adb}")
    
    if working_port:
        print(f"2. 使用可用端口: {working_port}")
    
    print("3. 手动指定ADB路径和端口")
    print("4. 显示详细解决方案")
    print("0. 退出")
    
    try:
        choice = input("\n请选择修复方案 (0-4): ").strip()
        
        if choice == "1" and mumu_adb:
            update_config(new_adb_path=mumu_adb)
        elif choice == "2" and working_port:
            update_config(new_device_uri=working_port)
        elif choice == "3":
            adb_path = input("请输入ADB路径: ").strip()
            device_uri = input("请输入设备地址 (如 127.0.0.1:7555): ").strip()
            if adb_path and device_uri:
                update_config(new_adb_path=adb_path, new_device_uri=device_uri)
        elif choice == "4":
            show_detailed_solutions()
        elif choice == "0":
            print("退出")
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n用户取消操作")

def show_detailed_solutions():
    """显示详细解决方案"""
    print("\n" + "=" * 50)
    print("详细解决方案")
    print("=" * 50)
    
    print("\n🔧 方案1: 以管理员身份运行")
    print("1. 关闭当前PowerShell")
    print("2. 右键PowerShell -> 以管理员身份运行")
    print("3. 重新运行: python adb_diagnostic.py")
    
    print("\n🔧 方案2: 重启ADB服务")
    print("1. 打开任务管理器")
    print("2. 结束所有adb.exe进程")
    print("3. 重新运行脚本")
    
    print("\n🔧 方案3: 检查MuMu设置")
    print("1. 打开MuMu模拟器")
    print("2. 点击右上角设置")
    print("3. 查找'开发者选项'或'ADB调试'")
    print("4. 确保ADB调试已启用")
    
    print("\n🔧 方案4: 使用MuMu自带ADB")
    print("1. 找到MuMu安装目录")
    print("2. 查找shell/adb.exe文件")
    print("3. 更新config.yaml中的adb_path")
    
    print("\n🔧 方案5: 重启计算机")
    print("如果以上方案都不行，重启计算机通常能解决端口冲突问题")

if __name__ == "__main__":
    main()
