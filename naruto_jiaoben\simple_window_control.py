#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的窗口控制 - 基础方法
"""

import time
import subprocess
import os

def activate_mumu_window():
    """激活MuMu窗口的简单方法"""
    try:
        # 方法1：使用Windows命令行工具
        print("尝试激活MuMu窗口...")
        
        # 使用tasklist查找MuMu进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq MuMuPlayer*'], 
                              capture_output=True, text=True)
        
        if 'MuMu' in result.stdout:
            print("✅ 找到MuMu进程")
        else:
            print("❌ 未找到MuMu进程")
            return False
        
        # 使用nircmd工具（如果有的话）
        nircmd_path = "nircmd.exe"
        if os.path.exists(nircmd_path):
            subprocess.run([nircmd_path, "win", "activate", "title", "MuMu模拟器12-1"])
            print("✅ 使用nircmd激活窗口")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 激活窗口失败: {e}")
        return False

def send_alt_p_simple():
    """发送Alt+P的简单方法"""
    try:
        print("发送Alt+P...")
        
        # 方法1：使用PowerShell发送按键
        ps_script = '''
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("%p")
        '''
        
        result = subprocess.run(['powershell', '-Command', ps_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PowerShell发送Alt+P成功")
            return True
        else:
            print(f"❌ PowerShell发送失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 发送Alt+P失败: {e}")
        return False

def send_hotkey_with_powershell(hotkey):
    """使用PowerShell发送快捷键"""
    try:
        print(f"使用PowerShell发送: {hotkey}")
        
        # 快捷键映射
        key_map = {
            'alt+p': '%p',
            'alt+q': '%q', 
            'alt+r': '%r',
            'alt+m': '%m',
            'ctrl+p': '^p',
            'ctrl+q': '^q',
            'f1': '{F1}',
            'f2': '{F2}',
            'f3': '{F3}',
            'f4': '{F4}',
        }
        
        sendkeys_code = key_map.get(hotkey.lower())
        if not sendkeys_code:
            print(f"❌ 不支持的快捷键: {hotkey}")
            return False
        
        ps_script = f'''
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("{sendkeys_code}")
        '''
        
        result = subprocess.run(['powershell', '-Command', ps_script], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ 发送 {hotkey} 成功")
            return True
        else:
            print(f"❌ 发送 {hotkey} 失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ PowerShell发送失败: {e}")
        return False

def test_multiple_hotkeys():
    """测试多个快捷键"""
    hotkeys_to_test = [
        'alt+p',
        'alt+q', 
        'alt+r',
        'alt+m',
        'ctrl+p',
        'ctrl+q',
        'f1',
        'f2',
        'f3',
        'f4',
    ]
    
    print("测试多个快捷键...")
    successful_keys = []
    
    for hotkey in hotkeys_to_test:
        print(f"\n测试: {hotkey}")
        
        if send_hotkey_with_powershell(hotkey):
            time.sleep(2)  # 等待观察效果
            
            response = input(f"快捷键 {hotkey} 是否有效果？(y/n/q退出): ").strip().lower()
            if response == 'y':
                successful_keys.append(hotkey)
                print(f"✅ {hotkey} 有效")
            elif response == 'q':
                break
            else:
                print(f"❌ {hotkey} 无效")
        else:
            print(f"❌ {hotkey} 发送失败")
    
    return successful_keys

def manual_test():
    """手动测试模式"""
    print("手动测试模式")
    print("输入要测试的快捷键，如: alt+p, ctrl+q, f1 等")
    print("输入 'quit' 退出")
    
    while True:
        hotkey = input("\n输入快捷键: ").strip()
        
        if hotkey.lower() == 'quit':
            break
        
        if send_hotkey_with_powershell(hotkey):
            print(f"✅ 已发送 {hotkey}")
        else:
            print(f"❌ 发送 {hotkey} 失败")

def main():
    print("简单窗口控制测试")
    print("=" * 40)
    
    print("选择测试模式:")
    print("1. 发送Alt+P")
    print("2. 测试多个快捷键")
    print("3. 手动测试模式")
    print("0. 退出")
    
    choice = input("请选择 (0-3): ").strip()
    
    if choice == "1":
        send_alt_p_simple()
    elif choice == "2":
        successful_keys = test_multiple_hotkeys()
        if successful_keys:
            print(f"\n✅ 有效的快捷键: {', '.join(successful_keys)}")
        else:
            print("\n❌ 未找到有效的快捷键")
    elif choice == "3":
        manual_test()
    elif choice == "0":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
