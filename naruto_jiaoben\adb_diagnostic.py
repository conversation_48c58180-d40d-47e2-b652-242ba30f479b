#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB连接诊断工具
"""

import subprocess
import sys
import os
import time
import socket
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_adb_path():
    """检查ADB路径"""
    print("=" * 60)
    print("1. 检查ADB路径")
    print("=" * 60)
    
    try:
        from core import settings
        adb_path = settings.ADB_PATH
        device_uri = settings.DEVICE_URI
        
        print(f"配置的ADB路径: {adb_path}")
        print(f"配置的设备地址: {device_uri}")
        
        # 检查文件是否存在
        if Path(adb_path).exists():
            print("✅ ADB文件存在")
        else:
            print("❌ ADB文件不存在")
            return False, adb_path, device_uri
        
        # 检查ADB版本
        try:
            result = subprocess.run([adb_path, 'version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ ADB可执行")
                print(f"ADB版本信息:\n{result.stdout}")
            else:
                print("❌ ADB无法执行")
                print(f"错误信息: {result.stderr}")
                return False, adb_path, device_uri
        except Exception as e:
            print(f"❌ ADB版本检查失败: {e}")
            return False, adb_path, device_uri
        
        return True, adb_path, device_uri
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False, None, None

def check_adb_server():
    """检查ADB服务器状态"""
    print("\n" + "=" * 60)
    print("2. 检查ADB服务器状态")
    print("=" * 60)
    
    # 检查端口5037是否被占用
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 5037))
        sock.close()
        
        if result == 0:
            print("⚠️ 端口5037已被占用（可能有ADB服务在运行）")
        else:
            print("✅ 端口5037空闲")
    except Exception as e:
        print(f"❌ 端口检查失败: {e}")

def check_processes():
    """检查相关进程"""
    print("\n" + "=" * 60)
    print("3. 检查相关进程")
    print("=" * 60)
    
    try:
        # 检查ADB进程
        result = subprocess.run(['tasklist'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            adb_processes = [line for line in lines if 'adb' in line.lower()]
            mumu_processes = [line for line in lines if 'mumu' in line.lower()]
            
            print("ADB相关进程:")
            if adb_processes:
                for proc in adb_processes:
                    print(f"  {proc.strip()}")
            else:
                print("  无ADB进程运行")
            
            print("\nMuMu相关进程:")
            if mumu_processes:
                for proc in mumu_processes:
                    print(f"  {proc.strip()}")
            else:
                print("  ❌ 无MuMu进程运行")
                return False
            
            return True
        else:
            print("❌ 无法获取进程列表")
            return False
            
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False

def try_fix_adb(adb_path):
    """尝试修复ADB连接"""
    print("\n" + "=" * 60)
    print("4. 尝试修复ADB连接")
    print("=" * 60)
    
    try:
        # 1. 杀死ADB服务器
        print("步骤1: 杀死现有ADB服务器...")
        subprocess.run([adb_path, 'kill-server'], 
                      capture_output=True, text=True, timeout=10)
        time.sleep(2)
        
        # 2. 启动ADB服务器
        print("步骤2: 启动ADB服务器...")
        result = subprocess.run([adb_path, 'start-server'], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ ADB服务器启动成功")
        else:
            print("❌ ADB服务器启动失败")
            print(f"错误信息: {result.stderr}")
            return False
        
        # 3. 检查设备列表
        print("步骤3: 检查设备列表...")
        result = subprocess.run([adb_path, 'devices'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 设备列表获取成功")
            print(f"设备列表:\n{result.stdout}")
            return True
        else:
            print("❌ 设备列表获取失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB修复失败: {e}")
        return False

def try_connect_device(adb_path, device_uri):
    """尝试连接设备"""
    print("\n" + "=" * 60)
    print("5. 尝试连接设备")
    print("=" * 60)
    
    try:
        print(f"尝试连接到: {device_uri}")
        result = subprocess.run([adb_path, 'connect', device_uri], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ 设备连接命令执行成功")
            print(f"连接结果: {result.stdout}")
            
            # 再次检查设备列表
            time.sleep(2)
            result = subprocess.run([adb_path, 'devices'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("设备列表:")
                print(result.stdout)
                
                # 检查是否有设备连接
                if device_uri.replace(':', '_') in result.stdout or 'device' in result.stdout:
                    print("✅ 设备连接成功")
                    return True
                else:
                    print("⚠️ 设备连接状态不明确")
                    return False
            else:
                print("❌ 无法获取设备列表")
                return False
        else:
            print("❌ 设备连接失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 设备连接异常: {e}")
        return False

def show_solutions():
    """显示解决方案"""
    print("\n" + "=" * 60)
    print("🔧 常见解决方案")
    print("=" * 60)
    
    print("\n1. 权限问题:")
    print("   - 以管理员身份运行PowerShell或命令提示符")
    print("   - 重新启动ADB服务")
    
    print("\n2. 端口冲突:")
    print("   - 关闭其他可能使用ADB的程序（Android Studio、其他模拟器等）")
    print("   - 重启计算机")
    
    print("\n3. MuMu模拟器问题:")
    print("   - 确保MuMu模拟器已完全启动")
    print("   - 在MuMu设置中启用ADB调试")
    print("   - 检查MuMu的ADB端口设置")
    
    print("\n4. 防火墙/杀毒软件:")
    print("   - 临时关闭防火墙和杀毒软件")
    print("   - 将ADB添加到白名单")
    
    print("\n5. 替代方案:")
    print("   - 使用MuMu自带的ADB工具")
    print("   - 尝试不同的端口（如7555、5555等）")

def main():
    """主函数"""
    print("ADB连接诊断工具")
    print("=" * 60)
    
    # 1. 检查ADB路径
    success, adb_path, device_uri = check_adb_path()
    if not success:
        print("\n❌ ADB路径检查失败，请检查config.yaml配置")
        show_solutions()
        return
    
    # 2. 检查ADB服务器
    check_adb_server()
    
    # 3. 检查进程
    mumu_running = check_processes()
    if not mumu_running:
        print("\n⚠️ MuMu模拟器未运行，请先启动MuMu模拟器")
    
    # 4. 尝试修复ADB
    if try_fix_adb(adb_path):
        # 5. 尝试连接设备
        if try_connect_device(adb_path, device_uri):
            print("\n🎉 ADB连接成功！")
            print("现在可以正常使用火影忍者自动化脚本了")
        else:
            print("\n❌ 设备连接失败")
            show_solutions()
    else:
        print("\n❌ ADB服务器启动失败")
        show_solutions()

if __name__ == "__main__":
    main()
