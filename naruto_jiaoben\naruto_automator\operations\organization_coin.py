import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_organization_prayer():
    """执行组织祈福任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行组织祈福任务...")
        
        # 使用通用滑动查找函数查找组织祈福功能
        if not utils.swipe_and_find_template("tpl1744705916833.png"):
            print("  -> [错误] 未找到组织祈福功能")
            return False
        time.sleep(20)
        # 等待组织祈福界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744705941113.png")
        print("  -> 组织祈福界面已加载")
        
        # 点击祈福按钮
        utils.wait_and_tap_template("tpl1744705964473.png")
        print("  -> 已点击祈福按钮")
        
        # 等待祈福确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744944802079.png")
        print("  -> 祈福确认界面已加载")
        
        # 点击确认祈福
        utils.wait_and_tap_template("tpl1744944836533.png")
        print("  -> 已确认祈福")
        
        time.sleep(10)  # 等待祈福完成
        
        # 检查是否有特殊弹窗
        current_screen = screen.get_screenshot()
        if vision.find_template(current_screen, "tpl1745200746938.png"):
            print("  -> 发现昨日奖励，执行特殊操作...")
            controller.tap(436, 193)
            utils.wait_for_template("tpl1745200909482.png")
            controller.tap(813, 271)
            time.sleep(1)
            controller.tap(808, 411)
            time.sleep(1)
            controller.tap(808, 571)
            time.sleep(1)
            controller.tap(190, 312)
            time.sleep(1)
            print("  -> 特殊操作完成")
        else:
            print("  -> 无特殊弹窗，执行默认操作...")
            controller.tap(750, 366)
        
        # 等待返回组织祈福界面（只等待，不点击）
        utils.wait_for_template("tpl1744705941113.png")
        print("  -> 返回组织祈福界面")
        
        # 点击返回按钮
        utils.wait_and_tap_template("tpl1744705195393.png")
        print("  -> 已点击返回按钮")
        
        # 等待最终确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744706151152.png")
        print("  -> 最终确认界面已加载")
        
        # 点击最终确认
        utils.wait_and_tap_template("tpl1744706168481.png")
        print("  -> 已点击最终确认")
        
        print("  -> 组织祈福任务完成")
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

def run_coin_collection():
    """执行铜币收集任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行铜币收集任务...")
        
        # 使用通用滑动查找函数查找铜币功能
        if not utils.swipe_and_find_template("tpl1744774309735.png"):
            print("  -> [错误] 未找到铜币功能")
            return False
        
        # 等待铜币界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744774406654.png")
        print("  -> 铜币界面已加载")
        
        # 点击收集按钮
        utils.wait_and_tap_template("tpl1744774466463.png")
        print("  -> 已点击收集按钮")
        
        time.sleep(5)  # 等待界面响应
        
        # 再次点击收集按钮
        utils.wait_and_tap_template("tpl1744774466463.png")
        print("  -> 已再次点击收集按钮")
        
        # 等待收集确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744774520479.png")
        print("  -> 收集确认界面已加载")
        
        # 点击确认收集
        utils.wait_and_tap_template("tpl1744774543791.png")
        print("  -> 已确认收集")
        
        print("  -> 铜币收集任务完成")
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

def run_organization_coin():
    """执行组织祈福和铜币收集的合并任务"""
    print("  -> 开始执行组织祈福和铜币收集任务...")
    
    # 执行组织祈福任务
    if run_organization_prayer():
        print("  -> 组织祈福任务完成")
    else:
        print("  -> 组织祈福任务失败")
        return False
    
    # 执行铜币收集任务
    if run_coin_collection():
        print("  -> 铜币收集任务完成")
    else:
        print("  -> 铜币收集任务失败")
        return False
    
    # 任务结束后：再次检查是否已成功返回主菜单
    if state_checker.is_in_main_menu():
        print("  -> 确认已成功返回主菜单。")
    else:
        print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
        utils.handle_timeout_and_return_to_main()
    
    print("  -> 组织祈福和铜币收集任务全部完成")
    return True 