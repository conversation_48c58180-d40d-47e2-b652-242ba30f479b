import time
from core import controller, screen, vision
from naruto_automator.states import state_checker
from naruto_automator import utils

def run():
    """
    执行"冒险副本"任务的主函数。
    增加了开始前和结束后对主界面的判断，并在判断失败时触发恢复机制。
    """
    print("  -> 开始'冒险副本'任务流程")

    # 任务开始前：检查是否在主菜单
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务

    try:
        utils.wait_and_tap_template("tpl1744706931346.png")
        print("  -> 已点击进入关卡")
        # 对应 tpl1744701578495.png
        utils.wait_and_tap_template("tpl1744701578495.png")
        print("  -> 进入精英副本")
        utils.wait_and_tap_template("tpl1744701785295.png")
        print("  -> 点击便携扫荡")
        utils.wait_and_tap_template("tpl1744701814810.png")
        print("  -> 点击扫荡")
        utils.wait_and_tap_template("tpl1744701856175.png")
        print("  -> 确认中")
        utils.wait_and_tap_template("tpl1744702195270.png", timeout=15)
        print("  -> 确认中")
        time.sleep(2) # 等待一下动画
        controller.tap(718, 144)
        time.sleep(0.5)
        controller.tap(718, 144)
        utils.wait_and_tap_template("tpl1744702420263.png", timeout=60) # 战斗时间可能很长
        utils.wait_and_tap_template("tpl1744702504096.png", timeout=60)
        print("  -> 战斗胜利，已点击返回地图")
        time.sleep(5) # 等待返回动画

        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()

    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

    print("  -> '冒险副本'任务流程结束")


def _select_dungeon_with_offset():
    """
    私有函数：处理你脚本中那个特殊的偏移点击。
    """
    print("  -> 查找关卡列表参考图标...")
    # 对应 tpl1744700702471.png
    ref_icon_name = "tpl1744700702471.png"
    
    # 这里我们不用封装好的wait_and_tap，因为需要自己处理坐标
    pos = utils.wait_for_template(ref_icon_name)
    
    # 计算目标坐标
    target_x = pos[0]
    target_y = pos[1] + 214 # 加上你原来的偏移量
    
    print(f"  -> 参考图标位置: {pos}，计算目标点击位置: ({target_x}, {target_y})")
    controller.tap(target_x, target_y)
    time.sleep(1) 