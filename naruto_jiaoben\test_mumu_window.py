#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MuMu窗口查找和快捷键发送
"""

import time
import win32gui
import win32con
import win32api

def find_mumu_window():
    """查找MuMu模拟器窗口"""
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            
            # 查找所有窗口，特别关注MuMu相关的
            if window_title:  # 只显示有标题的窗口
                windows.append((hwnd, window_title, class_name))
        return True
    
    windows = []
    win32gui.EnumWindows(enum_windows_callback, windows)
    
    print("所有可见窗口:")
    mumu_windows = []
    
    for i, (hwnd, title, class_name) in enumerate(windows):
        print(f"  {i+1}. 标题: {title}")
        print(f"      类名: {class_name}")
        print(f"      句柄: {hwnd}")
        
        # 查找MuMu相关窗口
        if 'mumu' in title.lower() or '模拟器' in title or 'MuMu' in title:
            mumu_windows.append((hwnd, title, class_name))
            print(f"      *** 这是MuMu相关窗口 ***")
        print()
    
    return mumu_windows

def test_window_control(hwnd, title):
    """测试窗口控制"""
    print(f"\n测试窗口控制: {title}")
    
    try:
        # 1. 检查窗口是否有效
        print("1. 检查窗口有效性...")
        if not win32gui.IsWindow(hwnd):
            print("❌ 窗口句柄无效")
            return False

        # 2. 尝试不同的方法将窗口调到前台
        print("2. 将窗口调到前台...")

        # 方法1：直接设置前台窗口
        try:
            win32gui.SetForegroundWindow(hwnd)
            print("✅ SetForegroundWindow 成功")
        except Exception as e:
            print(f"❌ SetForegroundWindow 失败: {e}")

            # 方法2：使用ShowWindow
            try:
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                print("✅ ShowWindow 成功")
            except Exception as e2:
                print(f"❌ ShowWindow 也失败: {e2}")

        time.sleep(1)
        
        # 3. 发送Alt+P快捷键
        print("3. 发送Alt+P快捷键...")
        VK_ALT = 0x12
        VK_P = ord('P')

        # 方法1：使用全局按键发送（更可靠）
        print("  -> 方法1: 全局按键发送...")
        try:
            win32api.keybd_event(VK_ALT, 0, 0, 0)  # Alt按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, 0, 0)    # P按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, win32con.KEYEVENTF_KEYUP, 0)    # P释放
            time.sleep(0.05)
            win32api.keybd_event(VK_ALT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
            print("✅ Alt+P已发送 (全局按键方法)")
        except Exception as e:
            print(f"❌ 全局按键发送失败: {e}")

        time.sleep(2)

        # 方法2：使用PostMessage
        print("  -> 方法2: PostMessage...")
        try:
            win32api.PostMessage(hwnd, win32con.WM_KEYDOWN, VK_ALT, 0)
            time.sleep(0.05)
            win32api.PostMessage(hwnd, win32con.WM_KEYDOWN, VK_P, 0)
            time.sleep(0.05)
            win32api.PostMessage(hwnd, win32con.WM_KEYUP, VK_P, 0)
            time.sleep(0.05)
            win32api.PostMessage(hwnd, win32con.WM_KEYUP, VK_ALT, 0)
            print("✅ Alt+P已发送 (PostMessage方法)")
        except Exception as e:
            print(f"❌ PostMessage发送失败: {e}")

        time.sleep(2)

        # 4. 将窗口最小化
        print("4. 将窗口最小化...")
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
            time.sleep(0.5)
            print("✅ 窗口已最小化")
        except Exception as e:
            print(f"❌ 窗口最小化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口控制失败: {e}")
        return False

def main():
    print("MuMu窗口查找和控制测试")
    print("=" * 50)
    
    # 1. 查找MuMu窗口
    mumu_windows = find_mumu_window()
    
    if not mumu_windows:
        print("❌ 未找到MuMu相关窗口")
        print("请确保MuMu模拟器已启动")
        return
    
    print(f"\n找到 {len(mumu_windows)} 个MuMu相关窗口:")
    for i, (hwnd, title, class_name) in enumerate(mumu_windows):
        print(f"  {i+1}. {title}")
    
    # 2. 选择要测试的窗口
    if len(mumu_windows) == 1:
        selected_window = mumu_windows[0]
        print(f"\n自动选择窗口: {selected_window[1]}")
    else:
        try:
            choice = int(input(f"\n请选择要测试的窗口 (1-{len(mumu_windows)}): ")) - 1
            if 0 <= choice < len(mumu_windows):
                selected_window = mumu_windows[choice]
                print(f"选择窗口: {selected_window[1]}")
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 输入无效")
            return
    
    # 3. 测试窗口控制
    hwnd, title, class_name = selected_window
    
    print(f"\n准备测试窗口控制...")
    print(f"窗口标题: {title}")
    print(f"窗口类名: {class_name}")
    print(f"窗口句柄: {hwnd}")
    
    input("\n按回车键开始测试...")
    
    if test_window_control(hwnd, title):
        print("\n✅ 窗口控制测试完成")
        print("请检查MuMu模拟器是否有反应（如打开了操作录制窗口）")
    else:
        print("\n❌ 窗口控制测试失败")

if __name__ == "__main__":
    main()
