import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_team_raid():
    """执行小队突袭任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行小队突袭任务...")
        
        # 使用通用滑动查找函数查找小队突袭功能
        if not utils.swipe_and_find_template("tpl1744704780049.png"):
            print("  -> [错误] 未找到小队突袭功能")
            return False
        
        # 等待小队突袭界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744704909385.png")
        print("  -> 小队突袭界面已加载")
        
        # 点击开始突袭按钮
        utils.wait_and_tap_template("tpl1744704942136.png")
        print("  -> 已点击开始突袭按钮")
        
        time.sleep(5)  # 等待界面响应
        
        # 等待突袭确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744704971593.png")
        print("  -> 突袭确认界面已加载")
        
        # 点击确认突袭
        utils.wait_and_tap_template("tpl1744704993993.png")
        print("  -> 已确认突袭")
        
        time.sleep(100)  # 等待突袭完成
        
        # 等待突袭完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744704942136.png")
        print("  -> 突袭完成界面已加载")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744704942136.png")
        print("  -> 已点击完成按钮")
        
        time.sleep(5)  # 等待界面响应
        
        # 等待最终确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744704971593.png")
        print("  -> 最终确认界面已加载")
        
        # 点击最终确认
        utils.wait_and_tap_template("tpl1744704993993.png")
        print("  -> 已点击最终确认")
        
        time.sleep(100)  # 等待第二次突袭完成
        
        # 等待最终完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744705362705.png")
        print("  -> 最终完成界面已加载")
        
        # 点击返回按钮
        utils.wait_and_tap_template("tpl1744705195393.png")
        print("  -> 已点击返回按钮")
        
        print("  -> 小队突袭任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc() 