#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MuMu宏录制完整流程控制器
实现：窗口管理 → 快捷键 → 操作录制窗口控制 → 图标点击
"""

import time
import subprocess
import win32gui
import win32con
import win32api
import cv2
import numpy as np
from pathlib import Path

class MuMuMacroFlowController:
    """MuMu宏录制完整流程控制器"""
    
    def __init__(self):
        self.mumu_main_window = None
        self.mumu_record_window = None
        self.find_windows()
    
    def find_windows(self):
        """查找MuMu相关窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        print("查找MuMu相关窗口...")
        
        for hwnd, title in windows:
            if title == "MuMu模拟器12-1":
                self.mumu_main_window = hwnd
                print(f"✅ 找到主窗口: {title}")
            elif "操作录制" in title or "MuMu 操作录制" in title:
                self.mumu_record_window = hwnd
                print(f"✅ 找到录制窗口: {title}")
        
        if not self.mumu_main_window:
            print("❌ 未找到MuMu模拟器12-1窗口")
        
        return self.mumu_main_window is not None
    
    def bring_window_to_front(self, hwnd, window_name):
        """将指定窗口置顶"""
        try:
            if not win32gui.IsWindow(hwnd):
                print(f"❌ {window_name} 窗口句柄无效")
                return False
            
            print(f"  -> 将 {window_name} 窗口置顶...")
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.5)
            
            print(f"✅ {window_name} 窗口已置顶")
            return True
            
        except Exception as e:
            print(f"❌ 置顶 {window_name} 窗口失败: {e}")
            return False
    
    def send_window_to_background(self, hwnd, window_name):
        """将指定窗口放到后台"""
        try:
            print(f"  -> 将 {window_name} 窗口放到后台...")
            win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
            time.sleep(0.3)
            
            print(f"✅ {window_name} 窗口已放到后台")
            return True
            
        except Exception as e:
            print(f"❌ 放置 {window_name} 窗口到后台失败: {e}")
            return False
    
    def send_alt_p_hotkey(self):
        """发送Alt+P快捷键"""
        try:
            print("  -> 发送Alt+P快捷键...")
            
            VK_ALT = 0x12
            VK_P = ord('P')
            
            # 全局按键发送
            win32api.keybd_event(VK_ALT, 0, 0, 0)  # Alt按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, 0, 0)    # P按下
            time.sleep(0.05)
            win32api.keybd_event(VK_P, 0, win32con.KEYEVENTF_KEYUP, 0)    # P释放
            time.sleep(0.05)
            win32api.keybd_event(VK_ALT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
            
            print("✅ Alt+P快捷键已发送")
            return True
            
        except Exception as e:
            print(f"❌ 发送Alt+P失败: {e}")
            return False
    
    def wait_for_record_window(self, timeout=10):
        """等待操作录制窗口出现"""
        print(f"  -> 等待操作录制窗口出现 (超时: {timeout}秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 重新查找窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if "操作录制" in window_title or "MuMu 操作录制" in window_title:
                        windows.append((hwnd, window_title))
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                self.mumu_record_window = windows[0][0]
                print(f"✅ 找到操作录制窗口: {windows[0][1]}")
                return True
            
            time.sleep(0.5)
        
        print("❌ 等待操作录制窗口超时")
        return False
    
    def take_screenshot(self, window_name="screenshot"):
        """截取当前屏幕截图"""
        try:
            import pyautogui
            
            timestamp = int(time.time())
            filename = f"{window_name}_{timestamp}.png"
            
            screenshot = pyautogui.screenshot()
            screenshot.save(filename)
            
            print(f"✅ 截图已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 截图失败: {e}")
            return None
    
    def click_image_template(self, template_path, threshold=0.8, region=None):
        """点击图像模板"""
        try:
            import pyautogui
            
            if not Path(template_path).exists():
                print(f"❌ 模板图片不存在: {template_path}")
                return False
            
            print(f"  -> 查找并点击图像: {template_path}")
            
            # 使用pyautogui查找图像
            location = pyautogui.locateOnScreen(template_path, confidence=threshold)
            
            if location:
                # 计算中心点
                center_x = location.left + location.width // 2
                center_y = location.top + location.height // 2
                
                # 点击
                pyautogui.click(center_x, center_y)
                print(f"✅ 已点击图像位置: ({center_x}, {center_y})")
                return True
            else:
                print(f"❌ 未找到图像: {template_path}")
                return False
                
        except Exception as e:
            print(f"❌ 点击图像失败: {e}")
            return False

    def click_fixed_position(self, x, y):
        """点击固定坐标位置"""
        try:
            import pyautogui

            print(f"  -> 点击固定坐标: ({x}, {y})")
            pyautogui.click(x, y)
            print(f"✅ 已点击坐标: ({x}, {y})")
            return True

        except Exception as e:
            print(f"❌ 点击固定坐标失败: {e}")
            return False

    def wait_with_progress(self, total_seconds):
        """带进度显示的等待"""
        print(f"  -> 开始等待 {total_seconds//60} 分钟 {total_seconds%60} 秒...")

        start_time = time.time()
        last_report_time = start_time

        while time.time() - start_time < total_seconds:
            current_time = time.time()
            elapsed = current_time - start_time
            remaining = total_seconds - elapsed

            # 每分钟报告一次进度
            if current_time - last_report_time >= 60:
                elapsed_minutes = int(elapsed // 60)
                remaining_minutes = int(remaining // 60)
                remaining_seconds = int(remaining % 60)

                print(f"  -> 已等待 {elapsed_minutes} 分钟，剩余 {remaining_minutes} 分钟 {remaining_seconds} 秒")
                last_report_time = current_time

            time.sleep(1)  # 每秒检查一次

        print(f"✅ 等待完成！总计等待了 {total_seconds//60} 分钟")
    
    def execute_complete_flow(self, test_mode=False):
        """执行完整流程"""
        if test_mode:
            print("开始执行MuMu宏录制测试流程（跳过40分钟等待）")
        else:
            print("开始执行MuMu宏录制完整流程")
        print("=" * 50)
        
        try:
            # 步骤1：调出MuMu模拟器窗口并置顶
            print("\n步骤1: 调出MuMu模拟器窗口并置顶")
            if not self.bring_window_to_front(self.mumu_main_window, "MuMu模拟器12-1"):
                return False
            
            # 步骤2：发送Alt+P快捷键
            print("\n步骤2: 发送Alt+P快捷键")
            if not self.send_alt_p_hotkey():
                return False
            
            # 步骤3：将MuMu窗口放到后台
            print("\n步骤3: 将MuMu窗口放到后台")
            if not self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1"):
                return False
            
            # 步骤4：等待操作录制窗口出现
            print("\n步骤4: 等待操作录制窗口出现")
            if not self.wait_for_record_window(timeout=5):
                print("❌ 操作录制窗口未出现，可能快捷键无效")
                return False
            
            # 步骤5：将操作录制窗口置顶
            print("\n步骤5: 将操作录制窗口置顶")
            if not self.bring_window_to_front(self.mumu_record_window, "MuMu 操作录制"):
                return False
            
            # 步骤6：自动点击第一个图标
            print("\n步骤6: 自动点击第一个图标")
            template1_path = "record_button1.png"

            if Path(template1_path).exists():
                print(f"  -> 使用模板图片: {template1_path}")
                if self.click_image_template(template1_path):
                    print("✅ 第一个图标点击成功")
                    time.sleep(2)  # 等待界面响应
                else:
                    print(f"❌ 点击第一个图标失败，尝试备用坐标")
                    # 备用方案：使用固定坐标点击
                    self.click_fixed_position(200, 100)  # 需要根据实际情况调整
                    time.sleep(2)
            else:
                print(f"⚠️ 模板图片不存在: {template1_path}，使用备用坐标")
                # 备用方案：使用固定坐标
                self.click_fixed_position(200, 100)  # 需要根据实际情况调整
                time.sleep(2)

            # 步骤7：自动点击第二个图标
            print("\n步骤7: 自动点击第二个图标")
            template2_path = "record_button2.png"

            if Path(template2_path).exists():
                print(f"  -> 使用模板图片: {template2_path}")
                if self.click_image_template(template2_path):
                    print("✅ 第二个图标点击成功")
                    time.sleep(2)  # 等待界面响应
                else:
                    print(f"❌ 点击第二个图标失败，尝试备用坐标")
                    # 备用方案：使用固定坐标点击
                    self.click_fixed_position(300, 150)  # 需要根据实际情况调整
                    time.sleep(2)
            else:
                print(f"⚠️ 模板图片不存在: {template2_path}，使用备用坐标")
                # 备用方案：使用固定坐标
                self.click_fixed_position(300, 150)  # 需要根据实际情况调整
                time.sleep(2)
            
            # 步骤8：等待模拟器响应
            print("\n步骤8: 等待模拟器响应...")
            time.sleep(3)  # 等待模拟器处理

            # 步骤9：将MuMu主窗口放到后台
            print("\n步骤9: 将MuMu主窗口放到后台")
            if not self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1"):
                print("⚠️ 无法将MuMu窗口放到后台，继续执行...")

            # 步骤10：关闭操作录制窗口（可选）
            print("\n步骤10: 处理操作录制窗口")
            if self.mumu_record_window:
                try:
                    self.send_window_to_background(self.mumu_record_window, "MuMu 操作录制")
                except:
                    print("⚠️ 操作录制窗口处理失败，继续执行...")

            # 步骤11：等待40分钟（测试模式跳过）
            if test_mode:
                print("\n步骤11: 测试模式 - 跳过40分钟等待，改为等待5秒...")
                time.sleep(5)
            else:
                print("\n步骤11: 等待40分钟...")
                self.wait_with_progress(30 * 60)  # 40分钟 = 2400秒

            # 步骤12：40分钟后调出MuMu窗口并点击第三个图标
            print("\n步骤12: 40分钟后 - 调出MuMu窗口并点击第三个图标")

            # 调出MuMu窗口
            if not self.bring_window_to_front(self.mumu_main_window, "MuMu模拟器12-1"):
                print("⚠️ 无法调出MuMu窗口，继续尝试...")

            time.sleep(2)  # 等待窗口稳定

            # 点击第三个图标
            template3_path = "record_button3.png"

            if Path(template3_path).exists():
                print(f"  -> 使用模板图片: {template3_path}")
                if self.click_image_template(template3_path):
                    print("✅ 第三个图标点击成功")
                    time.sleep(2)
                else:
                    print(f"❌ 点击第三个图标失败，尝试备用坐标")
                    self.click_fixed_position(400, 200)  # 备用坐标
                    time.sleep(2)
            else:
                print(f"⚠️ 模板图片不存在: {template3_path}，使用备用坐标")
                self.click_fixed_position(400, 200)  # 备用坐标
                time.sleep(2)

            # 步骤13：将MuMu模拟器放到后台
            print("\n步骤13: 将MuMu模拟器放到后台")
            if not self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1"):
                print("⚠️ 无法将MuMu模拟器放到后台，但流程已完成")

            # 步骤14：最终处理
            print("\n步骤14: 最终处理完成")
            time.sleep(2)  # 等待最终响应

            if test_mode:
                print("\n✅ 测试流程执行完成！")
            else:
                print("\n✅ 完整流程（包含40分钟等待）执行完成！")

            print("📋 流程总结:")
            print("  ✅ MuMu窗口管理")
            print("  ✅ Alt+P快捷键发送")
            print("  ✅ 操作录制窗口控制")
            print("  ✅ 第一个按钮点击")
            print("  ✅ 第二个按钮点击")
            if not test_mode:
                print("  ✅ 40分钟等待")
            print("  ✅ 第三个按钮点击")
            print("  ✅ MuMu模拟器已放到后台")

            return True
            
        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")

            # 即使出错也尝试将MuMu窗口放到后台
            try:
                print("尝试将MuMu窗口放到后台...")
                self.send_window_to_background(self.mumu_main_window, "MuMu模拟器12-1")
            except:
                print("无法将MuMu窗口放到后台")

            return False
    
    def create_template_guide(self):
        """创建模板图片指南"""
        print("\n📸 模板图片创建指南")
        print("=" * 40)
        print("需要创建以下模板图片:")
        print("1. record_button1.png - 操作录制窗口中的第一个按钮")
        print("2. record_button2.png - 操作录制窗口中的第二个按钮")
        print("3. record_button3.png - MuMu模拟器中的第三个按钮（40分钟后点击）")
        print()
        print("创建方法:")
        print("1. 运行 python screenshot_helper.py")
        print("2. 使用截图工具截取对应按钮区域")
        print("3. 自动保存为PNG格式")
        print("4. 文件会保存在当前目录下")
        print()
        print("流程说明:")
        print("- 前两个按钮在操作录制窗口中")
        print("- 第三个按钮在MuMu模拟器主窗口中（40分钟后使用）")

def run_auto_flow(test_mode=False):
    """自动运行完整流程（无需手动确认）"""
    print("MuMu宏录制自动流程")
    print("=" * 50)

    controller = MuMuMacroFlowController()

    if not controller.mumu_main_window:
        print("❌ 未找到MuMu模拟器窗口，请确保MuMu已启动")
        return False

    print("✅ 找到MuMu窗口，开始自动执行流程...")
    time.sleep(2)  # 给用户2秒准备时间

    return controller.execute_complete_flow(test_mode)

def run_test_flow():
    """测试流程（跳过40分钟等待）"""
    print("MuMu宏录制测试流程（跳过40分钟等待）")
    print("=" * 50)

    controller = MuMuMacroFlowController()

    if not controller.mumu_main_window:
        print("❌ 未找到MuMu模拟器窗口，请确保MuMu已启动")
        return False

    print("✅ 找到MuMu窗口，开始测试流程...")
    time.sleep(2)

    return controller.execute_complete_flow(test_mode=True)

def main():
    print("MuMu宏录制完整流程控制器")
    print("=" * 50)

    controller = MuMuMacroFlowController()

    if not controller.mumu_main_window:
        print("❌ 未找到MuMu模拟器窗口，请确保MuMu已启动")
        return

    print("\n选择操作:")
    print("1. 自动执行完整流程（包含40分钟等待）")
    print("2. 测试流程（跳过40分钟等待）")
    print("3. 手动执行完整流程")
    print("4. 查看模板图片创建指南")
    print("5. 测试窗口控制")
    print("0. 退出")

    choice = input("请选择 (0-5): ").strip()

    if choice == "1":
        run_auto_flow(test_mode=False)
    elif choice == "2":
        run_test_flow()
    elif choice == "3":
        controller.execute_complete_flow()
    elif choice == "4":
        controller.create_template_guide()
    elif choice == "5":
        # 测试窗口控制
        print("测试窗口控制...")
        controller.bring_window_to_front(controller.mumu_main_window, "MuMu模拟器12-1")
        time.sleep(2)
        controller.send_window_to_background(controller.mumu_main_window, "MuMu模拟器12-1")
    elif choice == "0":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    # 检查命令行参数
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        # 直接运行自动流程
        run_auto_flow()
    else:
        # 显示菜单
        main()
