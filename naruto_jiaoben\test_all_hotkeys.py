#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有可能的MuMu快捷键组合
"""

import time
import win32gui
import win32con
import win32api

def find_mumu_window():
    """查找MuMu模拟器窗口"""
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_title = win32gui.GetWindowText(hwnd)
            if 'MuMu模拟器12-1' == window_title:
                windows.append((hwnd, window_title))
        return True
    
    windows = []
    win32gui.EnumWindows(enum_windows_callback, windows)
    
    if windows:
        return windows[0][0]  # 返回窗口句柄
    else:
        print("❌ 未找到MuMu模拟器12-1窗口")
        return None

def send_hotkey_to_mumu(hwnd, key1_name, key2_name, vk_key1, vk_key2):
    """发送快捷键到MuMu窗口"""
    try:
        # 1. 将窗口调到前台
        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
        win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
        time.sleep(0.5)
        
        # 2. 发送快捷键
        print(f"发送 {key1_name}+{key2_name}...")
        
        # 全局按键发送
        win32api.keybd_event(vk_key1, 0, 0, 0)  # 修饰键按下
        time.sleep(0.05)
        win32api.keybd_event(vk_key2, 0, 0, 0)  # 目标键按下
        time.sleep(0.05)
        win32api.keybd_event(vk_key2, 0, win32con.KEYEVENTF_KEYUP, 0)  # 目标键释放
        time.sleep(0.05)
        win32api.keybd_event(vk_key1, 0, win32con.KEYEVENTF_KEYUP, 0)  # 修饰键释放
        
        # 3. 等待观察效果
        time.sleep(2)
        
        # 4. 将窗口最小化
        win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
        time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"❌ 发送 {key1_name}+{key2_name} 失败: {e}")
        return False

def test_all_mumu_hotkeys():
    """测试所有可能的MuMu快捷键"""
    
    # 查找MuMu窗口
    hwnd = find_mumu_window()
    if not hwnd:
        return
    
    print(f"✅ 找到MuMu窗口，句柄: {hwnd}")
    
    # 定义要测试的快捷键
    hotkeys_to_test = [
        # Alt组合键
        ("Alt", "P", 0x12, ord('P')),
        ("Alt", "Q", 0x12, ord('Q')),
        ("Alt", "R", 0x12, ord('R')),
        ("Alt", "M", 0x12, ord('M')),
        ("Alt", "S", 0x12, ord('S')),
        ("Alt", "T", 0x12, ord('T')),
        ("Alt", "F", 0x12, ord('F')),
        ("Alt", "1", 0x12, ord('1')),
        ("Alt", "2", 0x12, ord('2')),
        ("Alt", "3", 0x12, ord('3')),
        
        # Ctrl组合键
        ("Ctrl", "P", 0x11, ord('P')),
        ("Ctrl", "Q", 0x11, ord('Q')),
        ("Ctrl", "R", 0x11, ord('R')),
        ("Ctrl", "M", 0x11, ord('M')),
        ("Ctrl", "S", 0x11, ord('S')),
        ("Ctrl", "T", 0x11, ord('T')),
        
        # Shift组合键
        ("Shift", "P", 0x10, ord('P')),
        ("Shift", "Q", 0x10, ord('Q')),
        ("Shift", "R", 0x10, ord('R')),
        ("Shift", "M", 0x10, ord('M')),
        
        # 功能键
        ("", "F1", 0, 0x70),
        ("", "F2", 0, 0x71),
        ("", "F3", 0, 0x72),
        ("", "F4", 0, 0x73),
        ("", "F5", 0, 0x74),
        ("", "F6", 0, 0x75),
        ("", "F7", 0, 0x76),
        ("", "F8", 0, 0x77),
        ("", "F9", 0, 0x78),
        ("", "F10", 0, 0x79),
        ("", "F11", 0, 0x7A),
        ("", "F12", 0, 0x7B),
    ]
    
    print(f"\n准备测试 {len(hotkeys_to_test)} 个快捷键组合...")
    print("每个快捷键测试后会暂停，请观察MuMu是否有反应")
    print("=" * 60)
    
    successful_hotkeys = []
    
    for i, (key1_name, key2_name, vk_key1, vk_key2) in enumerate(hotkeys_to_test, 1):
        print(f"\n[{i}/{len(hotkeys_to_test)}] 测试快捷键: ", end="")
        
        if key1_name:
            hotkey_name = f"{key1_name}+{key2_name}"
            if send_hotkey_to_mumu(hwnd, key1_name, key2_name, vk_key1, vk_key2):
                print(f"✅ {hotkey_name} 发送成功")
            else:
                print(f"❌ {hotkey_name} 发送失败")
                continue
        else:
            # 单个功能键
            hotkey_name = key2_name
            try:
                # 将窗口调到前台
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)
                
                # 发送功能键
                print(f"发送 {key2_name}...")
                win32api.keybd_event(vk_key2, 0, 0, 0)
                time.sleep(0.05)
                win32api.keybd_event(vk_key2, 0, win32con.KEYEVENTF_KEYUP, 0)
                time.sleep(2)
                
                # 最小化窗口
                win32gui.ShowWindow(hwnd, win32con.SW_MINIMIZE)
                time.sleep(0.5)
                
                print(f"✅ {hotkey_name} 发送成功")
            except Exception as e:
                print(f"❌ {hotkey_name} 发送失败: {e}")
                continue
        
        # 询问用户是否有效果
        response = input(f"快捷键 {hotkey_name} 是否有效果？(y=有效果, n=无效果, s=跳过剩余, q=退出): ").strip().lower()
        
        if response == 'y':
            successful_hotkeys.append(hotkey_name)
            print(f"✅ {hotkey_name} 标记为有效")
        elif response == 's':
            print("跳过剩余测试")
            break
        elif response == 'q':
            print("退出测试")
            break
        else:
            print(f"❌ {hotkey_name} 标记为无效")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试完成！")
    
    if successful_hotkeys:
        print(f"\n✅ 找到 {len(successful_hotkeys)} 个有效快捷键:")
        for hotkey in successful_hotkeys:
            print(f"  - {hotkey}")
    else:
        print("\n❌ 未找到有效的快捷键")
        print("可能的原因:")
        print("1. MuMu的操作录制功能使用不同的快捷键")
        print("2. 需要在特定界面下才能触发")
        print("3. 快捷键可能被禁用或更改")
    
    return successful_hotkeys

def main():
    print("MuMu模拟器快捷键全面测试")
    print("=" * 50)
    print("此程序将测试各种可能的快捷键组合")
    print("请确保MuMu模拟器已启动")
    
    input("\n按回车键开始测试...")
    
    successful_hotkeys = test_all_mumu_hotkeys()
    
    if successful_hotkeys:
        print(f"\n🎉 测试成功！找到了有效的快捷键")
        
        # 保存结果
        try:
            with open("successful_hotkeys.txt", "w", encoding="utf-8") as f:
                f.write("MuMu模拟器有效快捷键:\n")
                for hotkey in successful_hotkeys:
                    f.write(f"- {hotkey}\n")
            print("✅ 结果已保存到 successful_hotkeys.txt")
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

if __name__ == "__main__":
    main()
