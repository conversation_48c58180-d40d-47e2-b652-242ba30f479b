#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本
用于清理临时文件、缓存文件等
"""

import os
import shutil
import glob
from pathlib import Path

def clean_pycache():
    """清理Python缓存文件"""
    print("清理Python缓存文件...")
    
    # 查找所有__pycache__目录
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(pycache_path)
                print(f"  已删除: {pycache_path}")
            except Exception as e:
                print(f"  删除失败: {pycache_path} - {e}")
    
    # 清理.pyc文件
    pyc_files = glob.glob('**/*.pyc', recursive=True)
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"  已删除: {pyc_file}")
        except Exception as e:
            print(f"  删除失败: {pyc_file} - {e}")

def clean_temp_files():
    """清理临时文件"""
    print("清理临时文件...")
    
    # 清理临时图片文件
    temp_patterns = [
        '**/*.tmp',
        '**/*.temp',
        '**/temp_*',
        '**/*_temp.*'
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern, recursive=True)
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"  已删除: {temp_file}")
            except Exception as e:
                print(f"  删除失败: {temp_file} - {e}")

def clean_log_files():
    """清理日志文件"""
    print("清理日志文件...")
    
    log_patterns = [
        '**/*.log',
        '**/naruto_log_*.txt'
    ]
    
    for pattern in log_patterns:
        log_files = glob.glob(pattern, recursive=True)
        for log_file in log_files:
            try:
                os.remove(log_file)
                print(f"  已删除: {log_file}")
            except Exception as e:
                print(f"  删除失败: {log_file} - {e}")

def clean_backup_files():
    """清理备份文件"""
    print("清理备份文件...")
    
    backup_patterns = [
        '**/*.bak',
        '**/*.backup',
        '**/*~'
    ]
    
    for pattern in backup_patterns:
        backup_files = glob.glob(pattern, recursive=True)
        for backup_file in backup_files:
            try:
                os.remove(backup_file)
                print(f"  已删除: {backup_file}")
            except Exception as e:
                print(f"  删除失败: {backup_file} - {e}")

def show_project_size():
    """显示项目大小"""
    print("计算项目大小...")
    
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
            except Exception:
                pass
    
    # 转换为合适的单位
    if total_size < 1024:
        size_str = f"{total_size} B"
    elif total_size < 1024 * 1024:
        size_str = f"{total_size / 1024:.2f} KB"
    elif total_size < 1024 * 1024 * 1024:
        size_str = f"{total_size / (1024 * 1024):.2f} MB"
    else:
        size_str = f"{total_size / (1024 * 1024 * 1024):.2f} GB"
    
    print(f"项目总大小: {size_str}")
    print(f"文件总数: {file_count}")

def main():
    """主函数"""
    print("="*50)
    print("项目清理工具")
    print("="*50)
    
    # 显示清理前的项目大小
    print("\n清理前:")
    show_project_size()
    
    print("\n开始清理...")
    
    # 执行清理操作
    clean_pycache()
    clean_temp_files()
    clean_log_files()
    clean_backup_files()
    
    print("\n清理完成!")
    
    # 显示清理后的项目大小
    print("\n清理后:")
    show_project_size()
    
    print("\n清理操作完成!")

if __name__ == "__main__":
    main()
