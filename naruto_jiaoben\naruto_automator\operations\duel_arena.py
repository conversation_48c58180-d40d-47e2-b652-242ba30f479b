import time
from naruto_automator import utils
from core import controller
from naruto_automator.states import state_checker
from core.controller import _run_adb_command
import sys
import os
from pathlib import Path

# 添加项目根目录到路径，以便导入 MuMuMacroFlowController
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from mumu_macro_flow_controller import MuMuMacroFlowController

def silent_tap(x, y):
    """静默点击，不输出日志（专用于决斗场避免刷屏）"""
    _run_adb_command(['shell', 'input', 'tap', str(x), str(y)])

def run_duel_arena_mumu_macro(battle_time_minutes=40):
    """
    执行决斗场任务 - 使用 MuMu 宏录制模式

    :param battle_time_minutes: 战斗时间（分钟）
    """
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return False

    try:
        print(f"  -> 开始执行决斗场任务，使用 MuMu 宏录制 {battle_time_minutes} 分钟")

        # 进入决斗场
        utils.wait_and_tap_template("tpl1751076426134.png")
        utils.wait_and_tap_template("tpl1751077055174.png")
        print("  -> 已进入决斗场")
        time.sleep(5)

        # 创建 MuMu 宏录制控制器
        print("  -> 初始化 MuMu 宏录制控制器...")
        mumu_controller = MuMuMacroFlowController()

        if not mumu_controller.mumu_main_window:
            print("  -> [错误] 未找到 MuMu 模拟器窗口")
            print("  -> 请确保:")
            print("     1. MuMu 模拟器已启动")
            print("     2. 窗口标题为 'MuMu模拟器12-1'")
            return False

        # 执行 MuMu 宏录制完整流程
        print("  -> ✅ 找到 MuMu 模拟器窗口，开始执行宏录制流程")

        # 根据战斗时间决定是否使用测试模式
        test_mode = battle_time_minutes < 30  # 小于30分钟使用测试模式

        if mumu_controller.execute_complete_flow(test_mode=test_mode):
            print(f"  -> ✅ MuMu 宏录制流程执行完成！")
        else:
            print(f"  -> ❌ MuMu 宏录制流程执行失败")
            return False

        time.sleep(10)

        # 继续执行奖励领取逻辑
        return _collect_duel_arena_rewards()

    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        utils.handle_timeout_and_return_to_main()
        return False
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()
        return False

def _collect_duel_arena_rewards():
    """
    领取决斗场奖励的辅助函数
    """
    print("  -> 开始领取决斗场奖励...")
    try:
    
        time.sleep(300)
        # 等待奖励界面并点击领取
        controller.tap(666, 524)
        time.sleep(3)
        controller.tap(666, 524)
        time.sleep(3)
        controller.tap(666, 524)
        utils.wait_for_template("tpl1744865146573.png", timeout=60)  # 只等待界面出现
        utils.wait_and_tap_template("tpl1744865146573.png")  # 等待并点击领取按钮
        print("  -> 进入奖励界面")

        time.sleep(3)

        # 循环领取奖励（原始.py的逻辑）
        print("  -> 开始循环领取奖励...")
        reward_count = 0
        while True:
            # 检测奖励按钮是否存在
            if utils.tap_template("tpl1745473756233.png"):
                reward_count += 1
                print(f"  -> 已领取第{reward_count}个奖励")
                time.sleep(10)
            else:
                print("  -> 没有更多奖励可领取，退出循环")
                break

            # 防止无限循环
            if reward_count >= 20:
                print("  -> 已领取20个奖励，退出循环")
                break

        # 原始代码的固定坐标奖励领取
        print("  -> 执行固定坐标奖励领取...")

        # 第一组奖励
        controller.tap(557, 560)
        time.sleep(2)
        controller.tap(1134, 188)
        time.sleep(3)

        # 第二组奖励
        controller.tap(714, 565)
        time.sleep(2)
        controller.tap(1134, 188)
        time.sleep(3)

        # 第三组奖励
        controller.tap(880, 566)
        time.sleep(2)
        controller.tap(1134, 188)
        time.sleep(3)

        # 第四组奖励
        controller.tap(1036, 559)
        time.sleep(2)
        controller.tap(1134, 188)
        time.sleep(15)

        # 最后的确认操作（原始逻辑）
        utils.wait_and_tap_template("tpl1744866072546.png")
        time.sleep(5)
        utils.wait_for_template("tpl1744778739345.png", timeout=10)
        utils.wait_and_tap_template("tpl1744775719622.png")

        print("  -> ✅ 决斗场奖励领取完成")

        # 最后的确认操作
        utils.wait_and_tap_template("tpl1744866072546.png")
        time.sleep(5)
        utils.wait_for_template("tpl1744778739345.png")  # 只等待界面出现
        utils.wait_and_tap_template("tpl1744775719622.png")  # 等待并点击确认按钮

        print("  -> ✅ 决斗场奖励领取完成")

        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()

        return True

    except TimeoutError:
        print("  -> [警告] 奖励领取超时，可能已经领取过或界面异常")
        # 即使超时也要检查主菜单状态
        print("  -> 检查当前是否在主菜单...")
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
            return True  # 虽然超时但已在主菜单，认为任务成功
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
            return False
    except Exception as e:
        print(f"  -> [警告] 奖励领取过程出现异常: {e}")
        # 即使异常也要检查主菜单状态
        print("  -> 检查当前是否在主菜单...")
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
            return True  # 虽然异常但已在主菜单，认为任务成功
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
            return False

def run_duel_arena_simple(battle_time_minutes=40):
    """
    执行决斗场任务 - 简单的时间控制模式
    
    :param battle_time_minutes: 战斗时间（分钟）
    """
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return False

    try:
        print(f"  -> 开始执行决斗场任务，使用 MuMu 宏录制 {battle_time_minutes} 分钟")

        # 进入决斗场
        utils.wait_and_tap_template("tpl1751076426134.png")
        utils.wait_and_tap_template("tpl1751077055174.png")
        print("  -> 已进入决斗场")
        time.sleep(5)

        # 使用 MuMu 宏录制流程替代连续点击
        print(f"  -> 开始执行 MuMu 宏录制流程 {battle_time_minutes} 分钟...")

        # 创建 MuMu 宏录制控制器
        mumu_controller = MuMuMacroFlowController()

        if not mumu_controller.mumu_main_window:
            print("  -> [错误] 未找到 MuMu 模拟器窗口")
            print("  -> 请确保:")
            print("     1. MuMu 模拟器已启动")
            print("     2. 窗口标题为 'MuMu模拟器12-1'")
            print("  -> 任务中断，仅支持 MuMu 宏录制模式")
            return False

        # 执行 MuMu 宏录制完整流程
        print("  -> ✅ 找到 MuMu 模拟器窗口，开始执行宏录制流程")

        # 根据战斗时间决定是否使用测试模式
        test_mode = battle_time_minutes < 30  # 小于30分钟使用测试模式

        if mumu_controller.execute_complete_flow(test_mode=test_mode):
            print(f"  -> ✅ MuMu 宏录制流程执行完成！")
        else:
            print(f"  -> ❌ MuMu 宏录制流程执行失败")
            return False

        time.sleep(10)

        # 使用统一的奖励领取函数
        return _collect_duel_arena_rewards()
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        utils.handle_timeout_and_return_to_main()
        return False
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()
        return False


# 决斗场函数 - 仅支持 MuMu 宏录制模式
def run():
    """标准模式，40分钟 MuMu 宏录制（推荐）"""
    return run_duel_arena_simple(battle_time_minutes=40)

def run_traditional():
    """30分钟 MuMu 宏录制模式"""
    return run_duel_arena_simple(battle_time_minutes=30)

def run_quick():
    """20分钟 MuMu 宏录制快速模式"""
    return run_duel_arena_simple(battle_time_minutes=20)

def run_time_control():
    """40分钟 MuMu 宏录制模式"""
    return run_duel_arena_simple(battle_time_minutes=40)

def run_mumu_macro():
    """MuMu 宏录制模式（40分钟）- 专用函数"""
    return run_duel_arena_mumu_macro(battle_time_minutes=40)

def run_mumu_macro_quick():
    """MuMu 宏录制快速模式（20分钟）"""
    return run_duel_arena_mumu_macro(battle_time_minutes=20)

def run_mumu_macro_test():
    """MuMu 宏录制测试模式（5分钟）"""
    return run_duel_arena_mumu_macro(battle_time_minutes=5)
