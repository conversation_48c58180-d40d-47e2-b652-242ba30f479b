#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB快捷键发送器
通过ADB向模拟器发送快捷键组合
"""

import subprocess
import time
import win32gui
import win32con
import win32api

class ADBHotkeySender:
    """ADB快捷键发送器"""
    
    def __init__(self):
        self.device_connected = self.check_device_connection()
        self.mumu_window = None
        self.find_mumu_window()
    
    def check_device_connection(self):
        """检查ADB设备连接"""
        try:
            # 直接使用adb命令检查设备
            result = subprocess.run([
                r"C:/Users/<USER>/PycharmProjects/jiaoben/platform-tools/adb.exe",
                "devices"
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and 'device' in result.stdout:
                print("✅ ADB设备连接正常")
                print(f"设备列表:\n{result.stdout}")
                return True
            else:
                print("❌ ADB设备未连接")
                print(f"ADB输出: {result.stdout}")
                return False
        except Exception as e:
            print(f"❌ ADB连接检查失败: {e}")
            return False

    def find_mumu_window(self):
        """查找MuMu模拟器窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 查找MuMu相关窗口，优先匹配确切名称
                exact_matches = ['MuMu模拟器12-1']
                general_keywords = ['mumu', 'netease', '网易', '模拟器', 'emulator']

                # 优先匹配确切名称
                if any(exact_name == window_title for exact_name in exact_matches):
                    windows.insert(0, (hwnd, window_title, class_name))  # 插入到开头
                elif any(keyword in window_title.lower() for keyword in general_keywords):
                    windows.append((hwnd, window_title, class_name))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        if windows:
            print("找到MuMu相关窗口:")
            for i, (hwnd, title, class_name) in enumerate(windows):
                print(f"  {i+1}. {title}")

            # 使用第一个找到的窗口（优先级最高的）
            self.mumu_window = windows[0][0]
            print(f"✅ 使用MuMu窗口: {windows[0][1]}")
            return True
        else:
            print("❌ 未找到MuMu模拟器窗口")
            print("请确保MuMu模拟器已启动，窗口标题应包含 'MuMu模拟器12-1'")
            return False

    def bring_mumu_to_front(self):
        """将MuMu窗口调到前台"""
        if not self.mumu_window:
            print("❌ MuMu窗口未找到")
            return False

        try:
            # 检查窗口是否仍然有效
            if not win32gui.IsWindow(self.mumu_window):
                print("❌ MuMu窗口已失效，重新查找...")
                if not self.find_mumu_window():
                    return False

            # 将窗口调到前台
            print("  -> 将MuMu窗口调到前台...")

            # 使用ShowWindow方法（更可靠）
            try:
                win32gui.ShowWindow(self.mumu_window, win32con.SW_RESTORE)
                win32gui.ShowWindow(self.mumu_window, win32con.SW_SHOW)
                time.sleep(0.5)  # 等待窗口切换
                print("✅ MuMu窗口已调到前台")
                return True
            except Exception as e:
                print(f"❌ 调用MuMu窗口到前台失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 窗口操作失败: {e}")
            return False

    def send_mumu_to_background(self):
        """将MuMu窗口放到后台"""
        if not self.mumu_window:
            print("❌ MuMu窗口未找到")
            return False

        try:
            print("  -> 将MuMu窗口放到后台...")
            win32gui.ShowWindow(self.mumu_window, win32con.SW_MINIMIZE)  # 最小化窗口
            time.sleep(0.2)

            print("✅ MuMu窗口已放到后台")
            return True

        except Exception as e:
            print(f"❌ 将MuMu窗口放到后台失败: {e}")
            return False
    
    def send_key_combination_with_window_management(self, key1, key2, hold_time=0.1):
        """
        发送组合键（带窗口管理）

        :param key1: 第一个按键（如Alt键）
        :param key2: 第二个按键（如P键）
        :param hold_time: 按键保持时间
        """
        if not self.device_connected:
            print("❌ 设备未连接，无法发送快捷键")
            return False

        try:
            print(f"发送组合键: {key1} + {key2} (带窗口管理)")

            # 步骤1：将MuMu窗口调到前台
            if not self.bring_mumu_to_front():
                print("❌ 无法将MuMu窗口调到前台，尝试直接发送快捷键...")

            # 步骤2：发送快捷键（使用全局按键方法）
            print("  -> 发送快捷键...")

            # 使用全局按键发送（测试证明最可靠）
            VK_ALT = 0x12
            VK_P = ord('P')
            VK_Q = ord('Q')

            key_map = {
                'alt': VK_ALT,
                'p': VK_P,
                'q': VK_Q
            }

            vk_key2 = key_map.get(key2.lower())
            if vk_key2:
                try:
                    # 全局按键发送
                    win32api.keybd_event(VK_ALT, 0, 0, 0)  # Alt按下
                    time.sleep(0.05)
                    win32api.keybd_event(vk_key2, 0, 0, 0)  # 目标键按下
                    time.sleep(0.05)
                    win32api.keybd_event(vk_key2, 0, win32con.KEYEVENTF_KEYUP, 0)  # 目标键释放
                    time.sleep(0.05)
                    win32api.keybd_event(VK_ALT, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放

                    print(f"✅ 已发送 {key1} + {key2} (全局按键方法)")
                except Exception as e:
                    print(f"❌ 发送快捷键失败: {e}")
                    return False
            else:
                print(f"❌ 不支持的按键: {key2}")
                return False

            # 步骤3：等待一下让快捷键生效
            time.sleep(1.0)

            # 步骤4：将MuMu窗口放到后台
            if self.mumu_window:
                self.send_mumu_to_background()

            return True

        except Exception as e:
            print(f"❌ 发送快捷键时出错: {e}")
            return False

    def send_key_combination(self, key1, key2, hold_time=0.1):
        """
        发送组合键（原始ADB方法）

        :param key1: 第一个按键（如Alt键）
        :param key2: 第二个按键（如Q键）
        :param hold_time: 按键保持时间
        """
        if not self.device_connected:
            print("❌ 设备未连接，无法发送快捷键")
            return False

        try:
            print(f"发送组合键: {key1} + {key2} (ADB方法)")

            # Android键码对照
            key_codes = {
                'alt': 57,      # ALT_LEFT
                'alt_left': 57, # ALT_LEFT
                'alt_right': 58,# ALT_RIGHT
                'q': 45,        # Q
                'p': 44,        # P
                'ctrl': 113,    # CTRL_LEFT
                'shift': 59,    # SHIFT_LEFT
                'tab': 61,      # TAB
                'enter': 66,    # ENTER
                'esc': 111,     # ESCAPE
                'space': 62,    # SPACE
                'f1': 131,      # F1
                'f2': 132,      # F2
                'f3': 133,      # F3
                'f4': 134,      # F4
            }

            key1_code = key_codes.get(key1.lower())
            key2_code = key_codes.get(key2.lower())

            if key1_code is None:
                print(f"❌ 未知按键: {key1}")
                return False

            if key2_code is None:
                print(f"❌ 未知按键: {key2}")
                return False

            # 直接使用ADB命令发送按键
            adb_path = r"C:/Users/<USER>/PycharmProjects/jiaoben/platform-tools/adb.exe"

            # 发送按键按下事件
            print(f"  -> 按下 {key1} (keycode: {key1_code})")
            result1 = subprocess.run([
                adb_path, "shell", "input", "keyevent", "--longpress", str(key1_code)
            ], capture_output=True, text=True, timeout=5)

            time.sleep(0.05)  # 短暂延迟

            print(f"  -> 按下 {key2} (keycode: {key2_code})")
            result2 = subprocess.run([
                adb_path, "shell", "input", "keyevent", str(key2_code)
            ], capture_output=True, text=True, timeout=5)

            time.sleep(hold_time)

            # 释放第一个按键（如果需要的话）
            print(f"  -> 释放按键组合")

            if result1.returncode == 0 and result2.returncode == 0:
                print(f"✅ 成功发送组合键: {key1} + {key2}")
                return True
            else:
                print(f"❌ 发送组合键失败")
                print(f"result1: {result1.stderr}")
                print(f"result2: {result2.stderr}")
                return False

        except Exception as e:
            print(f"❌ 发送快捷键时出错: {e}")
            return False
    
    def send_alt_p(self):
        """发送Alt+P组合键（带窗口管理）"""
        return self.send_key_combination_with_window_management('alt', 'p')

    def send_alt_p_adb(self):
        """发送Alt+P组合键（ADB方法）"""
        return self.send_key_combination('alt', 'p')

    def send_alt_q(self):
        """发送Alt+Q组合键（保留兼容性）"""
        return self.send_key_combination('alt', 'q')
    
    def send_multiple_alt_p(self, count=1, interval=1.0):
        """
        多次发送Alt+P

        :param count: 发送次数
        :param interval: 发送间隔（秒）
        """
        success_count = 0

        print(f"准备发送 {count} 次 Alt+P，间隔 {interval} 秒")

        for i in range(count):
            print(f"\n第 {i+1}/{count} 次发送 Alt+P:")

            if self.send_alt_p():
                success_count += 1
                print(f"✅ 第 {i+1} 次发送成功")
            else:
                print(f"❌ 第 {i+1} 次发送失败")

            if i < count - 1:  # 不是最后一次
                print(f"等待 {interval} 秒...")
                time.sleep(interval)

        print(f"\n发送完成: {success_count}/{count} 次成功")
        return success_count

    def send_multiple_alt_q(self, count=1, interval=1.0):
        """
        多次发送Alt+Q（保留兼容性）

        :param count: 发送次数
        :param interval: 发送间隔（秒）
        """
        success_count = 0

        print(f"准备发送 {count} 次 Alt+Q，间隔 {interval} 秒")

        for i in range(count):
            print(f"\n第 {i+1}/{count} 次发送 Alt+Q:")

            if self.send_alt_q():
                success_count += 1
                print(f"✅ 第 {i+1} 次发送成功")
            else:
                print(f"❌ 第 {i+1} 次发送失败")

            if i < count - 1:  # 不是最后一次
                print(f"等待 {interval} 秒...")
                time.sleep(interval)

        print(f"\n发送完成: {success_count}/{count} 次成功")
        return success_count
    
    def send_custom_hotkey(self, hotkey_string):
        """
        发送自定义快捷键
        
        :param hotkey_string: 快捷键字符串，如 "alt+q", "ctrl+c", "shift+tab"
        """
        try:
            # 解析快捷键字符串
            keys = [key.strip().lower() for key in hotkey_string.split('+')]
            
            if len(keys) != 2:
                print(f"❌ 快捷键格式错误，应为 'key1+key2' 格式: {hotkey_string}")
                return False
            
            return self.send_key_combination(keys[0], keys[1])
            
        except Exception as e:
            print(f"❌ 解析快捷键失败: {e}")
            return False
    
    def test_all_hotkeys(self):
        """测试常用快捷键"""
        test_hotkeys = [
            "alt+p",
            "alt+q",
            "alt+tab",
            "ctrl+c",
            "ctrl+v",
            "f1",
            "f2",
            "esc"
        ]
        
        print("测试常用快捷键...")
        
        for hotkey in test_hotkeys:
            print(f"\n测试快捷键: {hotkey}")
            
            if '+' in hotkey:
                self.send_custom_hotkey(hotkey)
            else:
                # 单个按键
                self.send_key_combination(hotkey, hotkey)  # 临时处理
            
            time.sleep(2)  # 等待2秒观察效果
            
            response = input(f"快捷键 {hotkey} 是否有效果？(y/n/q退出): ").strip().lower()
            if response == 'q':
                break
            elif response == 'y':
                print(f"✅ {hotkey} 有效")
            else:
                print(f"❌ {hotkey} 无效")

def main():
    """主函数"""
    print("ADB快捷键发送器")
    print("=" * 40)
    
    sender = ADBHotkeySender()
    
    if not sender.device_connected:
        print("请确保:")
        print("1. MuMu模拟器已启动")
        print("2. ADB连接正常")
        return
    
    while True:
        print("\n选择操作:")
        print("1. 发送 Alt+P (窗口管理)")
        print("2. 发送 Alt+P (ADB方法)")
        print("3. 多次发送 Alt+P")
        print("4. 发送 Alt+Q")
        print("5. 发送自定义快捷键")
        print("6. 测试常用快捷键")
        print("7. 检查设备连接")
        print("8. 重新查找MuMu窗口")
        print("0. 退出")
        
        choice = input("请选择 (0-8): ").strip()

        if choice == "1":
            sender.send_alt_p()

        elif choice == "2":
            sender.send_alt_p_adb()

        elif choice == "3":
            try:
                count = int(input("发送次数 (默认1): ") or "1")
                interval = float(input("发送间隔秒数 (默认1.0): ") or "1.0")
                sender.send_multiple_alt_p(count, interval)
            except ValueError:
                print("❌ 输入格式错误")

        elif choice == "4":
            sender.send_alt_q()

        elif choice == "5":
            hotkey = input("输入快捷键 (如 alt+p, ctrl+c): ").strip()
            if hotkey:
                sender.send_custom_hotkey(hotkey)
            else:
                print("❌ 快捷键不能为空")

        elif choice == "6":
            sender.test_all_hotkeys()

        elif choice == "7":
            sender.check_device_connection()

        elif choice == "8":
            sender.find_mumu_window()
            
        elif choice == "0":
            print("退出程序")
            break
            
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
