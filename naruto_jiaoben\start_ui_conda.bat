@echo off
chcp 65001 >nul
title 火影忍者自动化脚本 - UI界面 (Conda环境)

echo ========================================
echo  火影忍者自动化脚本 - UI界面
echo  使用Conda虚拟环境: game
echo ========================================
echo.

echo 正在激活conda环境: game...
call conda activate game

if errorlevel 1 (
    echo.
    echo 错误: 无法激活conda环境 'game'
    echo 请检查:
    echo 1. 是否已创建名为'game'的conda环境
    echo 2. 使用命令创建环境: conda create -n game python=3.8
    echo 3. 使用命令激活环境: conda activate game
    echo.
    pause
    exit /b 1
)

echo 环境激活成功！
echo.

echo 检查PyQt6是否已安装...
python -c "import PyQt6" 2>nul
if errorlevel 1 (
    echo PyQt6未安装，正在安装...
    pip install PyQt6
    if errorlevel 1 (
        echo.
        echo 错误: PyQt6安装失败
        echo 请手动安装: pip install PyQt6
        echo.
        pause
        exit /b 1
    )
    echo PyQt6安装成功！
) else (
    echo PyQt6已安装
)

echo.
echo 正在启动UI界面...
python run_ui.py

if errorlevel 1 (
    echo.
    echo 启动失败！请检查：
    echo 1. 是否在正确的目录下运行
    echo 2. 是否已安装所有依赖包
    echo 3. 使用命令安装依赖: pip install -r requirements_ui.txt
    echo.
    pause
) else (
    echo.
    echo UI界面已关闭
)

echo.
echo 正在退出conda环境...
call conda deactivate

pause 