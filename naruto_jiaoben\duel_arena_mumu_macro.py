#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
决斗场 - 使用MuMu操作录制窗口版本
"""

import time
from naruto_automator import utils
from naruto_automator.states import state_checker
from mumu_record_window_controller import Mu<PERSON>uRecordController

def run_duel_arena_with_mumu_macro(battle_time_minutes=40):
    """
    执行决斗场任务 - 使用MuMu操作录制窗口
    
    :param battle_time_minutes: 战斗时间（分钟）
    """
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return False

    try:
        print(f"  -> 开始执行决斗场任务，使用MuMu宏录制 {battle_time_minutes} 分钟")

        # 进入决斗场
        utils.wait_and_tap_template("tpl1751076426134.png")
        utils.wait_and_tap_template("tpl1751077055174.png")
        print("  -> 已进入决斗场")
        time.sleep(5)

        # 使用MuMu操作录制窗口控制器
        print("  -> 启动MuMu操作录制控制器...")
        controller = MuMuRecordController()
        
        if not controller.record_window:
            print("  -> [错误] 未找到MuMu操作录制窗口")
            print("  -> 请确保:")
            print("     1. MuMu模拟器已启动")
            print("     2. 操作录制窗口已打开")
            return False
        
        # 运行宏录制
        macro_file = r"E:\learningjiaoben\xuexiyong\jiaoben\naruto_jiaoben\mumu周胜9.10最新.mmor"
        print(f"  -> 开始运行宏录制 {battle_time_minutes} 分钟...")
        
        if controller.run_macro_for_duration(macro_file, battle_time_minutes):
            print(f"  -> ✅ 决斗场宏播放完成！")
        else:
            print(f"  -> ❌ 决斗场宏播放失败")
            return False
        
        time.sleep(10)
        
        # 领取决斗场奖励（保持原有逻辑）
        print("  -> 开始领取决斗场奖励...")
        try:
            from core import controller
            
            # 等待奖励界面并点击领取
            controller.tap(666, 524)
            time.sleep(3)
            controller.tap(666, 524)
            time.sleep(3)
            controller.tap(666, 524)
            utils.wait_for_template("tpl1744865118860.png", timeout=60)
            utils.wait_and_tap_template("tpl1744865146573.png")
            print("  -> 进入奖励界面")

            # 循环领取奖励
            print("  -> 开始循环领取奖励...")
            reward_count = 0
            while True:
                if utils.tap_template("tpl1745473756233.png"):
                    reward_count += 1
                    print(f"  -> 已领取第{reward_count}个奖励")
                    time.sleep(10)
                else:
                    print("  -> 没有更多奖励可领取，退出循环")
                    break
                    
                if reward_count >= 20:
                    print("  -> 已领取20个奖励，退出循环")
                    break
            
            # 固定坐标奖励领取
            print("  -> 执行固定坐标奖励领取...")
            
            controller.tap(557, 560)
            time.sleep(2)
            controller.tap(1134, 188)
            time.sleep(3)
            
            controller.tap(714, 565)
            time.sleep(2)
            controller.tap(1134, 188)
            time.sleep(3)
            
            controller.tap(880, 566)
            time.sleep(2)
            controller.tap(1134, 188)
            time.sleep(3)
            
            controller.tap(1036, 559)
            time.sleep(2)
            controller.tap(1134, 188)
            time.sleep(15)
            
            utils.wait_and_tap_template("tpl1744866072546.png")
            time.sleep(5)
            utils.wait_for_template("tpl1744778739345.png", timeout=10)
            utils.wait_and_tap_template("tpl1744775719622.png")
            
            print("  -> ✅ 决斗场奖励领取完成")
            
        except TimeoutError:
            print("  -> [警告] 奖励领取超时，可能已经领取过或界面异常")
        except Exception as e:
            print(f"  -> [警告] 奖励领取过程出现异常: {e}")
        
        time.sleep(5)
        
        # 检查是否返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        utils.handle_timeout_and_return_to_main()
        return False
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()
        return False

# 向后兼容的包装函数
def run():
    """标准的run函数，40分钟宏播放"""
    return run_duel_arena_with_mumu_macro(battle_time_minutes=40)

def run_quick():
    """快速模式（20分钟）"""
    return run_duel_arena_with_mumu_macro(battle_time_minutes=20)

if __name__ == "__main__":
    # 直接运行测试
    run()
