import cv2
import numpy as np
from pathlib import Path

# 资源文件夹路径
ASSETS_DIR = Path(__file__).parent.parent / "assets"

def find_template(screen, template_name, threshold=0.8):
    """
    在屏幕截图中查找模板图片的位置。

    :param screen: OpenCV格式的屏幕截图
    :param template_name: 在assets文件夹中的模板图片文件名，例如 'adventure_entry.png'
    :param threshold: 匹配阈值，默认为0.8
    :return: 如果找到，返回中心点坐标 (x, y)；否则返回 None
    """
    template_path = ASSETS_DIR / template_name
    if not template_path.exists():
        raise FileNotFoundError(f"模板图片未找到: {template_path}")

    template = cv2.imread(str(template_path), cv2.IMREAD_COLOR)
    if template is None:
        raise IOError(f"无法读取模板图片: {template_path}")

    h, w = template.shape[:2]
    result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

    if max_val >= threshold:
        center_x = max_loc[0] + w // 2
        center_y = max_loc[1] + h // 2
        print(f"  - 找到图片 '{template_name}' 在坐标: ({center_x}, {center_y})，相似度: {max_val:.2f}")
        return (center_x, center_y)
    else:
        return None

# 你可以将 ocr 和 region_color_detection 函数从你的旧脚本迁移到这里
# ... ocr_from_region ...
# ... region_color_detection ...

if __name__ == '__main__':
    # 这是一个简单的测试，当你直接运行此文件时会执行
    print("正在测试视觉功能...")

    # 1. 创建一个假的屏幕截图和模板用于测试
    screen_test = np.zeros((720, 1280, 3), dtype=np.uint8)
    template_test = np.zeros((100, 200, 3), dtype=np.uint8)
    cv2.putText(template_test, 'Button', (50, 60), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # 把模板放到假屏幕的中间
    y_offset, x_offset = 310, 540
    screen_test[y_offset:y_offset+100, x_offset:x_offset+200] = template_test
    
    # 2. 保存假的模板图片到assets文件夹
    if not ASSETS_DIR.exists():
        ASSETS_DIR.mkdir()
    cv2.imwrite(str(ASSETS_DIR / "test_template.png"), template_test)
    print("已创建测试模板 'assets/test_template.png'")

    # 3. 在假屏幕上查找假模板
    pos = find_template(screen_test, "test_template.png")
    
    if pos:
        print(f"测试成功！找到模板，位置: {pos}")
        assert pos == (540 + 100, 310 + 50), "坐标计算错误"
    else:
        print("测试失败！未找到模板。") 