__author__ = "tianyang"

import threading
import time
import random
from airtest.core.api import *
from airtest.core.android.adb import ADB
from airtest.core.api import *
import pytesseract
from PIL import Image
import re
import random
import os
from airtest.core.api import *
import pytesseract
from PIL import Image
import numpy as np
from airtest.core.api import *
from airtest.aircv import find_template  # 正确导入匹配函数
import time
import random
import threading
import cv2
import numpy as np
import traceback  # 添加traceback导入

# 允许外部设置设备连接URI
def init_device(device_uri=None):
    if device_uri:
        connect_device(device_uri)
    else:
        auto_setup(__file__)

# 不在导入时自动初始化，而是在每个函数开始时初始化
# init_device()

first_btn = Template(r"tpl1744699918742.png", record_pos=(0.456, -0.001), resolution=(1280, 720))
def 冒险副本():
    # 初始化设备
    # init_device()
    
    # 1. 等待并点击第一个按钮
    wait(first_btn, timeout=10.0)
    touch(first_btn)

    # 2. 等待第二个按钮出现
    second_btn = Template(r"tpl1744700269151.png", record_pos=(-0.262, -0.025), resolution=(1280, 720))
    wait(second_btn, timeout=10.0)

    # 3. 点击下方按钮（兼容写法）
    upper_pos = exists(Template(r"tpl1744700702471.png", record_pos=(-0.261, -0.104), resolution=(1280, 720)))
    if upper_pos:
        touch((upper_pos[0], upper_pos[1] + 214))  # 214是垂直偏移量
        
    three_btn = Template(r"tpl1744701578495.png", record_pos=(-0.327, -0.234), resolution=(1280, 720))
    wait(three_btn, timeout=10.0)
    touch(three_btn)

    four_btn = Template(r"tpl1744701785295.png", record_pos=(0.095, 0.236), resolution=(1280, 720))
    wait(four_btn, timeout=10.0)
    touch(four_btn)

    five_btn = Template(r"tpl1744701814810.png", record_pos=(0.308, 0.183), resolution=(1280, 720))
    wait(five_btn, timeout=10.0)
    touch(five_btn)

    six_btn = Template(r"tpl1744701856175.png", record_pos=(-0.094, 0.073), resolution=(1280, 720))
    wait(six_btn, timeout=10.0)
    touch(six_btn)

    wait(Template(r"tpl1744702179463.png", record_pos=(0.002, -0.03), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744702195270.png", record_pos=(-0.005, 0.073), resolution=(1280, 720)))
    
    # 点击屏幕中心
    touch([718, 144]) 
    touch([718, 144])

    wait(Template(r"tpl1744702394505.png", record_pos=(0.245, -0.001), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744702420263.png", record_pos=(0.352, -0.191), resolution=(1280, 720)))

    wait(Template(r"tpl1744702484407.png", record_pos=(-0.408, -0.219), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744702504096.png", record_pos=(0.457, -0.25), resolution=(1280, 720)))


# 冒险副本()


def 丰饶之间():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 10
    while max_swipes > 0 and not exists(Template(r"tpl1744703016071.png", record_pos=(-0.059, -0.084), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744703016071.png", record_pos=(-0.059, -0.084), resolution=(1280, 720)), "未找到目标图片")

    upper_pos1 = exists(Template(r"tpl1744703193584.png", record_pos=(-0.147, -0.108), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  

    wait(Template(r"tpl1744703384080.png", record_pos=(-0.034, 0.074), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744703419752.png", record_pos=(-0.086, 0.219), resolution=(1280, 720)))

    wait(Template(r"tpl1744703511570.png", record_pos=(-0.374, -0.245), resolution=(1280, 720)),timeout = 10)
    coordinates = [
        (718,144), (770,149), (837,165), 
        (717,209), (773,199), (767,267),
        (830,246), (1012,495)
    ]
    for _ in range(30):
        for pos in coordinates:  # 将target_positions改为coordinates
            touch(pos)
            sleep(0.8)
    

    sleep(20)
    wait(Template(r"tpl1744704170936.png", record_pos=(-0.119, 0.067), resolution=(1280, 720)), timeout=30.0)
    touch(Template(r"tpl1744704197617.png", record_pos=(0.46, -0.244), resolution=(1280, 720)))


# 丰饶之间()

def 小队突袭():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 10
    while max_swipes > 0 and not exists(Template(r"tpl1744704739160.png", record_pos=(0.011, -0.083), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744704739160.png", record_pos=(0.011, -0.083), resolution=(1280, 720)), "未找到目标图片")

    upper_pos1 = exists(Template(r"tpl1744704780049.png", record_pos=(0.013, -0.107), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
        
    wait(Template(r"tpl1744704909385.png", record_pos=(-0.084, -0.132), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744704942136.png", record_pos=(0.295, 0.241), resolution=(1280, 720)))
    sleep(5)
    wait(Template(r"tpl1744704971593.png", record_pos=(0.066, 0.124), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744704993993.png", record_pos=(0.426, 0.21), resolution=(1280, 720)))
    sleep(100.0)
    wait(Template(r"tpl1744704942136.png", record_pos=(0.295, 0.241), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744704942136.png", record_pos=(0.295, 0.241), resolution=(1280, 720)))
    sleep(5)
    wait(Template(r"tpl1744704971593.png", record_pos=(0.066, 0.124), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744704993993.png", record_pos=(0.426, 0.21), resolution=(1280, 720)))
    
    sleep(100.0)
    wait(Template(r"tpl1744705362705.png", record_pos=(-0.075, -0.003), resolution=(1280, 720)))

    touch(Template(r"tpl1744705195393.png", record_pos=(0.453, -0.252), resolution=(1280, 720)))
    

# 小队突袭()


def 组织祈福():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 10
    while max_swipes > 0 and not exists(Template(r"tpl1744705836073.png", record_pos=(-0.259, -0.083), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744705836073.png", record_pos=(-0.259, -0.083), resolution=(1280, 720)), "未找到目标图片")

    upper_pos1 = exists(Template(r"tpl1744705916833.png", record_pos=(-0.252, -0.106), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
        
    wait(Template(r"tpl1744705941113.png", record_pos=(-0.353, -0.067), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744705964473.png", record_pos=(-0.041, 0.173), resolution=(1280, 720)))
    wait(Template(r"tpl1744944802079.png", record_pos=(-0.034, -0.149), resolution=(1280, 720)),timeout = 10)

    touch(Template(r"tpl1744944836533.png", record_pos=(-0.041, 0.112), resolution=(1280, 720)))
    sleep(10)
    if exists(Template(r"tpl1745200746938.png", record_pos=(-0.154, -0.165), resolution=(1280, 720))):
        touch([436,193])
        wait(Template(r"tpl1745200909482.png", record_pos=(0.009, -0.174), resolution=(1280, 720)))
        touch([813,271])
        sleep(1)
        touch([808,411])
        sleep(1)
        touch([808,571])
        sleep(1)
        touch([190,312])
        sleep(1)
    else: 
        touch([750,366])


    wait(Template(r"tpl1744705941113.png", record_pos=(-0.353, -0.067), resolution=(1280, 720)),timeout = 10.0)

    touch(Template(r"tpl1744705195393.png", record_pos=(0.453, -0.252), resolution=(1280, 720)))
    
    wait(Template(r"tpl1744706151152.png", record_pos=(0.341, 0.223), resolution=(1280, 720)))
    touch(Template(r"tpl1744706168481.png", record_pos=(0.463, -0.252), resolution=(1280, 720)))

# 组织祈福()

def 每日分享和领取体力():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    max_swipes = 10
    while max_swipes > 0 and not exists(Template(r"tpl1750899784157.png", record_pos=(0.312, -0.026), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1750899784157.png", record_pos=(0.312, -0.026), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744706665297.png", record_pos=(0.054, -0.105), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
        
    wait(Template(r"tpl1744706698571.png", record_pos=(-0.056, -0.01), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744706712729.png", record_pos=(0.318, -0.173), resolution=(1280, 720)))
    wait(Template(r"tpl1744706728434.png", record_pos=(-0.377, 0.07), resolution=(1280, 720)),timeout = 10.0)

    touch(Template(r"tpl1744706750233.png", record_pos=(0.445, 0.237), resolution=(1280, 720)))
    sleep(10.0)
    wait(Template(r"tpl1744706784977.png", record_pos=(-0.438, -0.079), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744706810545.png", record_pos=(-0.463, -0.23), resolution=(1280, 720)))
    sleep(15.0)
    wait(Template(r"tpl1744706840257.png", record_pos=(-0.371, 0.073), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744706870780.png", record_pos=(0.459, -0.253), resolution=(1280, 720)))
    wait(Template(r"tpl1744706931346.png", record_pos=(0.414, 0.21), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744706911970.png", record_pos=(-0.455, -0.115), resolution=(1280, 720)))
    wait(Template(r"tpl1744706978078.png", record_pos=(-0.311, -0.009), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744706989577.png", record_pos=(-0.041, 0.184), resolution=(1280, 720)))
    wait(Template(r"tpl1744707023266.png", record_pos=(-0.002, -0.123), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744707039969.png", record_pos=(0.005, 0.073), resolution=(1280, 720)))
    touch(Template(r"tpl1744707069611.png", record_pos=(-0.177, 0.186), resolution=(1280, 720)))
    wait(Template(r"tpl1744707087265.png", record_pos=(-0.313, -0.009), resolution=(1280, 720)),timeout = 10.0)
    touch(Template(r"tpl1744707116785.png", record_pos=(0.362, -0.185), resolution=(1280, 720)))


#每日分享和领取体力()



def 招募():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 10
    while max_swipes > 0 and not exists(Template(r"tpl1744707534842.png", record_pos=(-0.058, -0.076), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744707534842.png", record_pos=(-0.058, -0.076), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744707569426.png", record_pos=(-0.059, -0.105), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
        
    wait(Template(r"tpl1744707593130.png", record_pos=(-0.426, 0.052), resolution=(1280, 720)),timeout = 10.0)
    if exists(Template(r"tpl1745032336513.png", record_pos=(0.186, 0.168), resolution=(1280, 720))):
        touch(Template(r"tpl1745032355871.png", record_pos=(0.186, 0.167), resolution=(1280, 720)))
        sleep(10.0)
        wait(Template(r"tpl1744707732151.png", record_pos=(-0.145, -0.214), resolution=(1280, 720)),timeout = 10.0)

        touch(Template(r"tpl1744707743818.png", record_pos=(-0.081, 0.16), resolution=(1280, 720)))
    wait(Template(r"tpl1745032433246.png", record_pos=(-0.431, -0.083), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744707600962.png", record_pos=(-0.444, -0.017), resolution=(1280, 720)))
    wait(Template(r"tpl1744707618634.png", record_pos=(0.098, 0.169), resolution=(1280, 720)),timeout = 10.0)

    touch(Template(r"tpl1744707627570.png", record_pos=(0.113, 0.168), resolution=(1280, 720)))
    
    sleep(10.0)
    wait(Template(r"tpl1744707732151.png", record_pos=(-0.145, -0.214), resolution=(1280, 720)),timeout = 10.0)

    touch(Template(r"tpl1744707743818.png", record_pos=(-0.081, 0.16), resolution=(1280, 720)))
    wait(Template(r"tpl1744707783978.png", record_pos=(0.348, 0.047), resolution=(1280, 720)),timeout = 10.0)

    touch(Template(r"tpl1744707797755.png", record_pos=(0.457, -0.252), resolution=(1280, 720)))


#招募()


def 生存挑战():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 30
    while max_swipes > 0 and not exists(Template(r"tpl1748352868069.png", record_pos=(0.195, -0.024), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1748352868069.png", record_pos=(0.195, -0.024), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744708340134.png", record_pos=(0.245, -0.111), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
    
    
    wait(Template(r"tpl1744708426147.png", record_pos=(0.405, 0.185), resolution=(1280, 720)), timeout=10.0)
    touch([606, 317])
    touch([606, 317])
    #还得修改
    sleep(5)


    touch(Template(r"tpl1744773918104.png", record_pos=(0.199, 0.233), resolution=(1280, 720)))
    
    wait(Template(r"tpl1744773839216.png", record_pos=(0.009, -0.128), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744773858903.png", record_pos=(-0.002, 0.07), resolution=(1280, 720)))
    sleep(5)
    touch([510, 631])

    wait(Template(r"tpl1744708426147.png", record_pos=(0.405, 0.185), resolution=(1280, 720)), timeout=10.0)
    

    touch(Template(r"tpl1744774096960.png", record_pos=(0.102, 0.225), resolution=(1280, 720)))
    wait(Template(r"tpl1744708959155.png", record_pos=(-0.166, -0.17), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744708970666.png", record_pos=(0.0, 0.18), resolution=(1280, 720)))
    wait(Template(r"tpl1744709008411.png", record_pos=(-0.001, -0.127), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744709023483.png", record_pos=(0.002, 0.073), resolution=(1280, 720)))
    wait(Template(r"tpl1744709008411.png", record_pos=(-0.001, -0.127), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744709023483.png", record_pos=(0.002, 0.073), resolution=(1280, 720)))

    sleep(60.0)

    wait(Template(r"tpl1744709179107.png", record_pos=(-0.308, -0.232), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744709185587.png", record_pos=(0.462, -0.254), resolution=(1280, 720)))

# 调用函数
# 生存挑战()




def 开启火影():
    start_app("com.tencent.KiHan")
    sleep(60.0)
    
    
    wait(Template(r"tpl1744721756506.png", record_pos=(-0.023, 0.137), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744721832434.png", record_pos=(0.002, 0.193), resolution=(1280, 720)))
    sleep(60)

# 调用函数（顶格写，不要缩进）
# 开启火影()




def 铜币():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 30
    while max_swipes > 0 and not exists(Template(r"tpl1744774283591.png", record_pos=(-0.254, -0.023), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744774283591.png", record_pos=(-0.254, -0.023), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744774309735.png", record_pos=(-0.254, -0.109), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
    wait(Template(r"tpl1744774406654.png", record_pos=(-0.002, -0.194), resolution=(1280, 720)), timeout=10.0)
    
    touch(Template(r"tpl1744774466463.png", record_pos=(0.058, 0.173), resolution=(1280, 720)))

    sleep(5)
    touch(Template(r"tpl1744774466463.png", record_pos=(0.058, 0.173), resolution=(1280, 720)))
    wait(Template(r"tpl1744774520479.png", record_pos=(0.007, -0.192), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744774543791.png", record_pos=(0.345, -0.192), resolution=(1280, 720)))

# 铜币()



def 任务集会所():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 30
    while max_swipes > 0 and not exists(Template(r"tpl1744774649143.png", record_pos=(-0.055, -0.028), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744774649143.png", record_pos=(-0.055, -0.028), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744774678712.png", record_pos=(-0.057, -0.114), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
    wait(Template(r"tpl1744774696751.png", record_pos=(-0.27, -0.093), resolution=(1280, 720)), timeout=10.0)
    
    touch([655, 166])
    wait(Template(r"tpl1744774832008.png", record_pos=(0.0, -0.126), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744774841607.png", record_pos=(-0.106, 0.071), resolution=(1280, 720)))
    touch([1182, 452])
    sleep(1)
    touch([1182, 452])
    sleep(1)
    
    sleep(10)
    touch([1168, 271])
    if exists(Template(r"tpl1744943660413.png", record_pos=(0.236, 0.17), resolution=(1280, 720))):
        touch(Template(r"tpl1744943706232.png", record_pos=(0.228, 0.171), resolution=(1280, 720)))
    else :
        touch([1182, 452])
    wait(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)))
    

    sleep(5)
    wait(Template(r"tpl1744774696751.png", record_pos=(-0.27, -0.093), resolution=(1280, 720)), timeout=10.0)
    touch([1168, 390])
    if exists(Template(r"tpl1744943660413.png", record_pos=(0.236, 0.17), resolution=(1280, 720))):
        touch(Template(r"tpl1744943706232.png", record_pos=(0.228, 0.171), resolution=(1280, 720)))
    else :
        touch([1182, 452])
    wait(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)))
    
    sleep(5)
    wait(Template(r"tpl1744774696751.png", record_pos=(-0.27, -0.093), resolution=(1280, 720)), timeout=10.0)
    touch([1168, 493])
    if exists(Template(r"tpl1744943660413.png", record_pos=(0.236, 0.17), resolution=(1280, 720))):
        touch(Template(r"tpl1744943706232.png", record_pos=(0.228, 0.171), resolution=(1280, 720)))
    else :
        touch([1182, 452])
    wait(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1744775133750.png", record_pos=(0.416, 0.214), resolution=(1280, 720)))
    sleep(6)
    wait(Template(r"tpl1744775700414.png", record_pos=(0.41, 0.225), resolution=(1280, 720)),timeout =10)
    touch(Template(r"tpl1744775719622.png", record_pos=(0.459, -0.258), resolution=(1280, 720)))

    
# 任务集会所()




def 决斗场():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 30
    while max_swipes > 0 and not exists(Template(r"tpl1744776198846.png", record_pos=(0.142, -0.028), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1744776198846.png", record_pos=(0.142, -0.028), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1744776270446.png", record_pos=(0.14, -0.11), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
    sleep(5)
    启动同步点击器()
    sleep(60)
    
def 启动同步点击器(
    coordinates=[(718,144),(770,149),(837,165),(717,209),(773,199),(767,267),(830,246),(1157,607),(1022,579),(696,464)],
    运行时间=1800,
    随机偏移=3,
    最小间隔=0.001,
    最大间隔=0.003
):
    """
    启动多线程同步点击器
    参数：
        coordinates: 点击坐标列表 [(x1,y1), (x2,y2), ...]
        运行时间: 总运行时间(秒)
        随机偏移: 点击坐标随机偏移范围(像素)
        最小间隔: 最小点击间隔(秒)
        最大间隔: 最大点击间隔(秒)
    """
    # 初始化设备
    # init_device()
    
    class 点击器:
        def __init__(self):
            # auto_setup(__file__)
            try:
                self.dev = device()
                self.adb = ADB(serialno=self.dev.serialno)
                print(f"点击器初始化成功，设备: {self.dev}")
            except Exception as e:
                print(f"点击器初始化失败: {e}")
                raise e
        
        def 同步点击(self, 坐标, 结束时间):
            while time.time() < 结束时间:
                try:
                    x, y = 坐标
                    self.adb.shell(f"input tap {x+random.randint(-随机偏移,随机偏移)} {y+random.randint(-随机偏移,随机偏移)}")
                    time.sleep(random.uniform(最小间隔, 最大间隔))
                except Exception as e:
                    print(f"点击出错: {e}")
                    time.sleep(1)
                    try:
                        self.dev = device()
                        self.adb = ADB(serialno=self.dev.serialno)
                    except Exception as e2:
                        print(f"重新连接设备失败: {e2}")

    print("正在初始化同步点击器...")
    try:
        点击器实例 = 点击器()
        结束时间 = time.time() + 运行时间
        threads = []

        print(f"启动{len(coordinates)}个点击线程...")
        for i, 坐标 in enumerate(coordinates):
            t = threading.Thread(target=点击器实例.同步点击, args=(坐标, 结束时间), name=f"点击线程-{i}")
            t.daemon = True
            t.start()
            threads.append(t)
            print(f"线程{i}已启动，坐标:{坐标}")

        print("所有线程已启动，开始同步点击...")
        for t in threads:
            t.join()
    except Exception as e:
        print(f"启动同步点击器失败: {e}")
        traceback.print_exc()


# 决斗场()
# 启动同步点击器()

def 决斗奖励():
    sleep(180)
    touch([718, 144])
    sleep(1)
    touch([718, 144])
    sleep(1)
    touch([718, 144])
    sleep(1)
    touch([718, 144])
    sleep(10)
    
    wait(Template(r"tpl1744865118860.png", record_pos=(-0.292, 0.003), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744865146573.png", record_pos=(0.127, 0.223), resolution=(1280, 720)))

    #明天完善 
    target = Template(r"tpl1745473756233.png", record_pos=(0.387, -0.132), resolution=(1280, 720))
    while True:
        pos = exists(target)
        if pos:
            touch(pos)
            sleep(10)

        else:
            break

    touch([557, 560])
    sleep(2)
    touch([1134, 188])
    sleep(3)

    touch([714, 565])
    sleep(2)
    touch([1134, 188])
    sleep(3)
    touch([880, 566])
    sleep(2)
    touch([1134, 188])
    sleep(3)

    touch([1036, 559])
    sleep(2)
    touch([1134, 188])
    sleep(15)
    

    touch(Template(r"tpl1744866072546.png", record_pos=(-0.461, -0.099), resolution=(1280, 720)))


    sleep(5)
    
    wait(Template(r"tpl1744778739345.png", record_pos=(-0.454, -0.105), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744775719622.png", record_pos=(0.459, -0.258), resolution=(1280, 720)))



# 决斗奖励()

def 领取奖励():
    # 完全保持原有代码结构
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    sleep(6)
    
    # 坐标横向分布（从左到右）
    horizontal_buttons = [
        (513, 563),  # 最左侧按钮
        
        (609, 589),

        (739, 559),  # 左中按钮
        
        (609, 585),

        (1034, 569), # 右中按钮
        
        (609, 583),

        (1184, 563), # 最右侧按钮
        
        (609, 581),

        
    ]

    # 依次点击所有按钮
    for btn in horizontal_buttons:
        touch(btn)
        sleep(1)  # 每次点击间隔1秒

    sleep(10)  # 最终等待
    touch(Template(r"tpl1744780400328.png", record_pos=(0.455, -0.256), resolution=(1280, 720)))
    wait(Template(r"tpl1744780427446.png", record_pos=(0.417, 0.22), resolution=(1280, 720)),timeout = 10 )
    touch(Template(r"tpl1744780455793.png", record_pos=(-0.454, -0.013), resolution=(1280, 720)))
    sleep(20)
    wait(Template(r"tpl1744780501625.png", record_pos=(0.238, -0.087), resolution=(1280, 720)),timeout = 10 )
    touch(Template(r"tpl1744780524297.png", record_pos=(0.096, -0.074), resolution=(1280, 720)))
    sleep(10)
    if exists(Template(r"tpl1744780547913.png", record_pos=(0.0, -0.165), resolution=(1280, 720))):
        touch(Template(r"tpl1744780558065.png", record_pos=(-0.002, 0.151), resolution=(1280, 720)))
    else:
        touch([622,90])
        sleep(3)
    sleep(5)

    wait(Template(r"tpl1744866390107.png", record_pos=(0.0, -0.18), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1744866412537.png", record_pos=(0.0, 0.163), resolution=(1280, 720)))
    sleep(10) 
    
    target = Template(r"tpl1745554121705.png", record_pos=(-0.025, 0.122), resolution=(1280, 720))
    while True:
        pos = exists(target)
        if pos:
            touch(pos)
            sleep(10)

        else:
            break

    touch(Template(r"tpl1744780862904.png", record_pos=(-0.258, 0.05), resolution=(1280, 720)))
    wait(Template(r"tpl1744781523417.png", record_pos=(-0.073, -0.023), resolution=(1280, 720)),timeout = 10 )
    touch(Template(r"tpl1744781529825.png", record_pos=(0.059, 0.043), resolution=(1280, 720)))
    sleep(20)
    touch(Template(r"tpl1744781578861.png", record_pos=(0.001, 0.086), resolution=(1280, 720)))
    touch([718, 144])
    sleep(6)
    touch(Template(r"tpl1744780920850.png", record_pos=(0.424, -0.265), resolution=(1280, 720)))
    sleep(30)
    wait(Template(r"tpl1744781648648.png", record_pos=(0.402, 0.205), resolution=(1280, 720)),timeout = 10)
    touch([59, 276])
    sleep(5)
    touch(Template(r"tpl1744781699248.png", record_pos=(-0.148, 0.209), resolution=(1280, 720)))
    sleep(10)
    touch(Template(r"tpl1744781721912.png", record_pos=(0.421, -0.19), resolution=(1280, 720)))

# 以下是注释掉的函数调用，防止在导入时自动执行
# 领取奖励()






def ocr(img_template, region=None):
    """
    对图片进行OCR识别，支持指定区域识别
    
    参数:
        img_template: 可以是图片路径或Template对象
        region: 可选的识别区域，格式为(x, y, width, height)，默认为整张图片
        
    返回:
        识别到的文字，如果未识别到则返回"未检测到文字"
    """
    import os
    import numpy as np
    from PIL import Image
    os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
    import paddle
    from paddleocr import PaddleOCR
    from airtest.core.cv import Template
    
    try:
        # 处理不同类型的输入
        if isinstance(img_template, Template):
            img_path = img_template.filepath
        elif isinstance(img_template, str):
            img_path = img_template
        else:
            return "不支持的输入类型"
        
        # 初始化OCR模型
        ocr_engine = PaddleOCR(use_angle_cls=True, lang="ch", use_gpu=False, show_log=False)
        
        # 如果指定了区域，先裁剪图像
        if region is not None:
            try:
                # 使用PIL库读取和裁剪图像
                from PIL import Image
                img = Image.open(img_path)
                if img is not None:
                    x, y, w, h = region
                    # 确保裁剪区域在图像范围内
                    width, height = img.size
                    x = max(0, min(x, width-1))
                    y = max(0, min(y, height-1))
                    w = min(w, width-x)
                    h = min(h, height-y)
                    
                    # 裁剪图像
                    cropped_img = img.crop((x, y, x+w, y+h))
                    
                    # 保存临时文件
                    temp_path = "temp_ocr_region.png"
                    cropped_img.save(temp_path)
                    
                    # 使用临时文件进行OCR
                    result = ocr_engine.ocr(temp_path, cls=True)
                    
                    # 删除临时文件
                    import os
                    if os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                        except:
                            pass
                else:
                    return "无法读取图片"
            except Exception as e:
                return f"裁剪图像出错: {str(e)}"
        else:
            # 对整张图片进行OCR
            result = ocr_engine.ocr(img_path, cls=True)
        
        # 提取文字结果
        if result and len(result) > 0:
            if isinstance(result[0], list):
                if len(result[0]) > 0 and len(result[0][0]) >= 2:
                    return result[0][0][1][0]
                else:
                    return "未检测到文字"
            else:
                if len(result) > 0 and len(result[0]) >= 2:
                    return result[0][1][0]
                else:
                    return "未检测到文字"
        else:
            return "未检测到文字"
    except Exception as e:
        return f"OCR识别出错: {str(e)}"
def region_ocr_to_int(region):
    """
    在指定区域进行OCR识别并转换为整数
    
    参数:
        region: OCR识别区域，格式为(x, y, width, height)，必须指定
        
    返回:
        如果识别并转换成功，返回整数值
        否则返回None
    """
    # 使用绝对路径保存截图，避免路径问题
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_screen = os.path.join(current_dir, "temp_screen.png")
    
    try:
        # 截取屏幕
        snapshot(filename=temp_screen)
        
        # 进行OCR识别
        region_text = ocr(temp_screen, region=region)
        
        # 转换为整数
        if region_text:
            # 移除所有非数字字符（包括逗号、空格、点等）
            digits_only = ''.join(c for c in region_text if c.isdigit())
            if digits_only:
                return int(digits_only)
    finally:
        # 清理临时文件
        if os.path.exists(temp_screen):
            try:
                os.remove(temp_screen)
            except:
                pass
    
    return None


def 积分赛():
    wait(first_btn, timeout=10.0)
    touch(first_btn)
    
    max_swipes = 30
    while max_swipes > 0 and not exists(Template(r"tpl1745475950187.png", record_pos=(0.139, -0.025), resolution=(1280, 720))):
        swipe((0.8, 0.5), (0.2, 0.5), duration=0.5)
        max_swipes -= 1
    assert_exists(Template(r"tpl1745475950187.png", record_pos=(0.139, -0.025), resolution=(1280, 720))), "未找到目标图片"

    upper_pos1 = exists(Template(r"tpl1745475991391.png", record_pos=(0.141, -0.105), resolution=(1280, 720)))
    if upper_pos1:
        touch((upper_pos1[0], upper_pos1[1] + 214))  
    sleep(10)
    touch([666,524])
    sleep(5)
    touch([666,524])
    sleep(5)
    touch([666,524])
    sleep(5)
    # 主循环部分（执行6次）
    
    wait(Template(r"tpl1745998709035.png", record_pos=(-0.308, -0.221), resolution=(1280, 720)), timeout=10)


    execution_count = 0
    while execution_count < 6:
        # 战力检测部分
        wait(Template(r"tpl1745476047512.png", record_pos=(0.215, -0.188), resolution=(1280, 720)), timeout=10)
        touch(Template(r"tpl1745476068545.png", record_pos=(0.413, 0.186), resolution=(1280, 720)))
        sleep(10)

        # 战力检测循环
        while True:
            # 我的战力
            numberme = region_ocr_to_int(region=(271, 329, 86, 36))
            print(f"第{execution_count+1}次执行 - 我的战力:", numberme)
            wait(Template(r"tpl1745637697775.png", record_pos=(-0.284, -0.136), resolution=(1280, 720)),timeout = 10)
            touch(Template(r"tpl1745637731371.png", record_pos=(-0.328, 0.015), resolution=(1280, 720)))

            # 其他战力检测
            number1 = region_ocr_to_int(region=(840, 234, 114, 30))  # 954-840=114, 264-234=30
            
            number2 = region_ocr_to_int(region=(843, 353, 113, 29))  # 956-843=113, 382-353=29
            number3 = region_ocr_to_int(region=(839, 469, 117, 33))  # 956-839=117, 502-469=33
            number4 = region_ocr_to_int(region=(842, 587, 117, 31))  # 959-842=117, 618-587=31

            # 创建数字字典（过滤无效值）
            valid_numbers = {
                num: pos for num, pos in zip(
                    [number1, number2, number3, number4],
                    [[1032, 203], [1032, 320], [1032, 435], [1032, 562]]
                ) if num is not None
            }

            if not valid_numbers:
                print("战力识别失败，重试...")
                sleep(2)
                continue

            min_number = min(valid_numbers.keys())
            min_position = valid_numbers[min_number]

            if min_number < numberme+500:
                print(f"选择战力{min_number}的对手")
                touch(min_position)
                sleep(15)
                wait(Template(r"tpl1745480697856.png", record_pos=(-0.463, 0.161), resolution=(1280, 720)),timeout = 30)
                touch(Template(r"tpl1745480697856.png", record_pos=(-0.463, 0.161), resolution=(1280, 720)))
                
                wait(Template(r"tpl1745478112790.png", record_pos=(-0.071, 0.068), resolution=(1280, 720)), timeout=30)
                touch(Template(r"tpl1745478141734.png", record_pos=(0.103, 0.209), resolution=(1280, 720)))
                
                sleep(20)
                touch([666,524])
                sleep(20)
                execution_count += 1
                break  # 跳出战力检测循环，继续主循环
            else:
                print("没有合适对手，刷新后重试...")
                touch(Template(r"tpl1745477729006.png", record_pos=(-0.254, 0.148), resolution=(1280, 720)))
                sleep(5)
                # 自动跳转回战力检测部分（通过内层while True循环）

    print("已完成执行")
    wait(Template(r"tpl1745481348134.png", record_pos=(0.219, -0.195), resolution=(1280, 720)),timeout = 10)
    touch(Template(r"tpl1745481363805.png", record_pos=(0.454, -0.255), resolution=(1280, 720)))


# 积分赛()
def region_color_detection(region):
    """
    在指定区域进行颜色检测，返回主要颜色（紫色或橙色）
    
    参数:
        region: 颜色检测区域，格式为(x, y, width, height)，必须指定
        
    返回:
        如果检测成功，返回字符串 "紫色" 或 "橙色"
        否则返回None
    """
    # 使用绝对路径保存截图，避免路径问题
    import os
    from PIL import Image
    import numpy as np
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    temp_screen = os.path.join(current_dir, "temp_screen.png")
    
    try:
        # 截取屏幕
        snapshot(filename=temp_screen)
        
        # 打开截图并裁剪指定区域
        img = Image.open(temp_screen)
        region_img = img.crop((region[0], region[1], 
                              region[0] + region[2], region[1] + region[3]))
        
        # 转换为numpy数组并计算主要颜色
        pixels = np.array(region_img)
        mean_color = np.mean(pixels, axis=(0, 1)).astype(int)
        r, g, b = mean_color[:3]
        
        # 定义典型紫色和橙色的RGB值
        purple_ref = (136, 50, 127)  # 紫色参考值，基于您的紫色样本
        orange_ref = (120, 61, 3)    # 橙色参考值，基于您的橙色样本
        
        # 计算与紫色和橙色的欧几里得距离
        purple_distance = ((r - purple_ref[0])**2 + (g - purple_ref[1])**2 + (b - purple_ref[2])**2)**0.5
        orange_distance = ((r - orange_ref[0])**2 + (g - orange_ref[1])**2 + (b - orange_ref[2])**2)**0.5
        
        # 辅助判断：橙色通常 R>G>B，紫色通常 R≈B>G
        if r > g > b:  # 橙色特征
            return "橙色"
        elif b > g and abs(r - b) < 50:  # 紫色特征：B高，R和B接近
            return "紫色"
        
        # 如果特征不明显，依赖距离
        if purple_distance < orange_distance:
            return "紫色"
        else:
            return "橙色"
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_screen):
            try:
                os.remove(temp_screen)
            except:
                pass
    
    return None


def 升级装备():
    # 查找装备
    wait(first_btn, timeout=10.0)
    sleep(6)
    touch(Template(r"tpl1745056969032.png", record_pos=(-0.283, 0.243), resolution=(1280, 720)))

    wait(Template(r"tpl1745057005807.png", record_pos=(-0.353, 0.167), resolution=(1280, 720)), timeout=10.0)
    touch(Template(r"tpl1745057016806.png", record_pos=(-0.428, 0.161), resolution=(1280, 720)))
    # 最多循环5次
    max_attempts = 5
    attempts = 0
    
    while attempts < max_attempts:
        attempts += 1
        print(f"尝试第 {attempts}/{max_attempts} 次")
        
        
        color1 = region_color_detection((184.96, 257.84, 34, 23))  # 模板1
        color2 = region_color_detection((362.88, 259.4, 34, 22))   # 模板2
        color3 = region_color_detection((518.16, 249.0, 45, 31))   # 模板3
        color4 = region_color_detection((180.24, 404.84, 44, 48))  # 模板4
        color5 = region_color_detection((354.48, 398.0, 39, 52))   # 模板5
        color6 = region_color_detection((518.16, 408.84, 45, 40))  # 模板6

        # 打印结果（可选，用于调试）
        print("Color 1:", color1)
        print("Color 2:", color2)
        print("Color 3:", color3)
        print("Color 4:", color4)
        print("Color 5:", color5)
        print("Color 6:", color6)

        # 存储紫色区域的位置信息
        purple_positions = []
        
        # 检查每个位置是否为紫色
        if color1 == "紫色":
            purple_positions.append([202, 269])  # 模板1中心坐标
        if color2 == "紫色":
            purple_positions.append([380, 270])  # 模板2中心坐标
        if color3 == "紫色":
            purple_positions.append([540, 265])  # 模板3中心坐标
        if color4 == "紫色":
            purple_positions.append([202, 429])  # 模板4中心坐标
        if color5 == "紫色":
            purple_positions.append([374, 424])  # 模板5中心坐标
        if color6 == "紫色":
            purple_positions.append([540, 429])  # 模板6中心坐标
        
        # 如果找到紫色位置，随机选择一个点击
        if purple_positions:
            import random
            # 尝试点击直到成功或尝试完所有紫色位置
            tried_positions = []
            while purple_positions:
                # 随机选择一个未尝试过的位置
                available_positions = [pos for pos in purple_positions if pos not in tried_positions]
                if not available_positions:
                    print("所有紫色装备都已尝试，但未找到目标")
                    # 使用返回按钮的一般位置坐标，而不是无效的模板
                    touch(Template(r"tpl1745558640199.png", record_pos=(0.455, -0.252), resolution=(1280, 720)))  # 点击左上角返回按钮的大致位置
                    sleep(2)
                    break
                    
                chosen_position = random.choice(available_positions)
                tried_positions.append(chosen_position)
                print(f"选择的紫色位置: {chosen_position}")
                
                # 点击选中的位置
                touch(chosen_position)
                sleep(2)
                
                # 使用wait函数检查模板是否存在，可以设置timeout参数
                target_template = Template(r"tpl1745558097285.png", record_pos=(0.36, 0.098), resolution=(1280, 720))
                try:
                    # 使用wait代替exists，添加timeout参数
                    wait(target_template, timeout=10)
                    touch(target_template)
                    
                    wait(Template(r"tpl1745558962639.png", record_pos=(0.102, -0.131), resolution=(1280, 720)), timeout=10.0)
                    touch(Template(r"tpl1745558973352.png", record_pos=(0.146, 0.151), resolution=(1280, 720)))
                    wait(Template(r"tpl1745559136680.png", record_pos=(-0.014, 0.129), resolution=(1280, 720)), timeout=10.0)
                    touch(Template(r"tpl1745559144992.png", record_pos=(0.177, 0.12), resolution=(1280, 720)))
                    if exists(Template(r"tpl1745561229286.png", record_pos=(0.001, -0.116), resolution=(1280, 720))):
                        touch(Template(r"tpl1745561242717.png", record_pos=(0.184, -0.116), resolution=(1280, 720)))
                        touch(Template(r"tpl1745560152018.png", record_pos=(-0.414, -0.235), resolution=(1280, 720)))
                        sleep(5)
                        touch(Template(r"tpl1745560176571.png", record_pos=(-0.397, -0.227), resolution=(1280, 720)))
                        sleep(5)
                        touch(Template(r"tpl1745561299052.png", record_pos=(0.452, -0.252), resolution=(1280, 720)))
                        # 直接跳出所有循环
                        attempts = max_attempts  # 强制结束外部循环
                        break  # 结束内部循环
                    else:
                        sleep(25)
                        if exists(Template(r"tpl1745560245746.png", record_pos=(0.055, 0.114), resolution=(1280, 720))):
                            touch(Template(r"tpl1745560152018.png", record_pos=(-0.414, -0.235), resolution=(1280, 720)))
                            if exists(Template(r"tpl1745560316389.png", record_pos=(0.147, 0.157), resolution=(1280, 720))):
                                touch(Template(r"tpl1745560345266.png", record_pos=(0.152, 0.155), resolution=(1280, 720)))
                                wait(Template(r"tpl1745560446643.png", record_pos=(0.362, 0.098), resolution=(1280, 720)),timeout = 20)
                                touch(Template(r"tpl1745560446643.png", record_pos=(0.362, 0.098), resolution=(1280, 720)),)
                                wait(Template(r"tpl1745560503403.png", record_pos=(-0.045, -0.047), resolution=(1280, 720)),timeout = 20)
                                touch(Template(r"tpl1745560509339.png", record_pos=(0.002, 0.158), resolution=(1280, 720)))
                                # 正常完成流程，结束所有循环
                                attempts = max_attempts  # 强制结束外部循环
                                break  # 结束内部循环
                            else:
                                touch([137,64])
                                # 结束内部循环，但继续外部循环尝试其他装备
                                break
                        else:
                            touch(Template(r"tpl1745560152018.png", record_pos=(-0.414, -0.235), resolution=(1280, 720)))
                            sleep(5)
                            touch(Template(r"tpl1745560176571.png", record_pos=(-0.397, -0.227), resolution=(1280, 720)))
                            # 结束内部循环，但继续外部循环尝试其他装备
                            break
                except:
                    # 超时未找到模板时会抛出异常
                    print(f"位置 {chosen_position} 没有找到目标模板，尝试其他位置")
        else:
            print("没有找到紫色装备")
            # 如果没有找到紫色装备，继续下一次循环尝试
    
    # 循环结束后的处理
    if attempts >= max_attempts:
        print(f"已完成 {max_attempts} 次尝试")
    else:
        print(f"在第 {attempts} 次尝试中成功完成")


# 升级装备()





def 启动点击器(
    coordinates=[(718,144),(770,149),(837,165),(717,209),(773,199),(767,267),(830,246),(1157,607),(1022,579),(696,464)],
    目标图片路径=r"tpl1745763565781.png",
    计数阈值=3,
    检测间隔=0.001,
    随机偏移=3,
    最小间隔=0.001,
    最大间隔=0.003,
    匹配阈值=0.8,
    设备URI=None
):
    """
    启动多线程同步点击器，通过图像检测控制结束
    参数：
        coordinates: 点击坐标列表 [(x1,y1), (x2,y2), ...]
        目标图片路径: 用于匹配的目标图片路径
        计数阈值: 检测到目标图片的次数达到此值时停止
        检测间隔: 图像检测的时间间隔(秒)
        随机偏移: 点击坐标随机偏移范围(像素)
        最小间隔: 最小点击间隔(秒)
        最大间隔: 最大点击间隔(秒)
        匹配阈值: 图像匹配的置信度阈值(0-1)
        设备URI: 设备连接URI，如果为None则使用已连接设备
    """
    # 全局计数器和控制变量
    计数器 = 0
    计数器锁 = threading.Lock()
    停止标志 = threading.Event()
    
    # 处理Template对象
    if isinstance(目标图片路径, Template):
        目标图片路径 = 目标图片路径.filepath
    elif not isinstance(目标图片路径, str):
        print(f"警告: 目标图片路径类型不支持 ({type(目标图片路径)})，尝试转换为字符串")
        目标图片路径 = str(目标图片路径)

    # 确保设备已连接
    try:
        if 设备URI is not None:
            # 如果提供了设备URI，尝试连接
            connect_device(设备URI)
            print(f"已连接设备: {设备URI}")
        elif not G.DEVICE:
            # 如果没有设备连接，尝试自动连接
            print("未检测到设备，尝试自动连接...")
            auto_setup(__file__)
    except Exception as e:
        print(f"设备连接失败: {e}")
        print("请确保设备已连接或提供正确的设备URI")
        return

    class 点击器:
        def __init__(self):
            try:
                self.dev = device()
                self.adb = ADB(serialno=self.dev.serialno)
                print(f"点击器初始化成功，设备: {self.dev}")
            except Exception as e:
                print(f"点击器初始化失败: {e}")
                raise e
        
        def 同步点击(self, 坐标, 停止标志):
            """在指定坐标上持续点击，直到停止标志触发"""
            while not 停止标志.is_set():
                try:
                    x, y = 坐标
                    self.adb.shell(f"input tap {x+random.randint(-随机偏移,随机偏移)} {y+random.randint(-随机偏移,随机偏移)}")
                    time.sleep(random.uniform(最小间隔, 最大间隔))
                except Exception as e:
                    print(f"点击出错: {e}")
                    time.sleep(1)
                    try:
                        self.dev = device()
                        self.adb = ADB(serialno=self.dev.serialno)
                    except Exception as e2:
                        print(f"重新连接设备失败: {e2}")

    def 检测图片(点击器实例):
        """定期检测屏幕是否出现目标图片，更新计数器"""
        nonlocal 计数器
        try:
            while not 停止标志.is_set():
                # 使用exists函数检测是否存在目标图片
                匹配结果 = exists(Template(目标图片路径, threshold=匹配阈值))
                
                if 匹配结果:
                    with 计数器锁:
                        计数器 += 1
                        print(f"检测到目标图片，当前计数: {计数器}")
                        if 计数器 >= 计数阈值:
                            停止标志.set()
                            print("计数达到阈值，停止点击器")
                
                time.sleep(检测间隔)
        except Exception as e:
            print(f"图片检测出错: {e}")
            traceback.print_exc()

    print("正在初始化同步点击器...")
    try:
        # 初始化点击器实例
        点击器实例 = 点击器()
        threads = []

        # 启动图像检测线程
        检测线程 = threading.Thread(target=检测图片, args=(点击器实例,), name="图像检测线程")
        检测线程.daemon = True
        检测线程.start()
        threads.append(检测线程)
        print("图像检测线程已启动")

        # 启动点击线程
        print(f"启动{len(coordinates)}个点击线程...")
        for i, 坐标 in enumerate(coordinates):
            t = threading.Thread(target=点击器实例.同步点击, args=(坐标, 停止标志), name=f"点击线程-{i}")
            t.daemon = True
            t.start()
            threads.append(t)
            print(f"线程{i}已启动，坐标:{坐标}")

        print("所有线程已启动，开始同步点击...")
        for t in threads:
            t.join()
        
        print(f"点击器已停止，总计检测到目标图片 {计数器} 次")
    except Exception as e:
        print(f"启动同步点击器失败: {e}")
        traceback.print_exc()

# 启动点击器()  # 注释掉直接调用，避免导入时自动运行
def 连败():

    
    while True:
        # 检查并点击第一个模板
        if exists(Template(r"tpl1746340308768.png", record_pos=(0.009, 0.244), resolution=(1280, 720))):
            touch([650,672])
        else:
            touch([1163, 596])  # 如果找不到第一个模板则点击备用坐标
    
        # 检查并点击第二个模板
        if exists(Template(r"tpl1746340338927.png", record_pos=(0.299, 0.184), resolution=(1280, 720))):
            touch([1000,580])
        else:
            touch([1163, 596])  # 如果找不到第二个模板则点击备用坐标
    
        # 最后再点击一次备用坐标
        touch([1163, 596])
    
        # 可选：添加短暂延迟防止过快点击
        # sleep(0.05)

# 调用函数（注意这里应该顶格写，没有缩进）
# 连败()


def 打包零食():
    while True:  # 无限循环
        # 等待界面出现
        wait(Template(r"tpl1746544552314.png", record_pos=(0.355, 0.18), resolution=(1280, 720)), timeout=15)
        
        # 随机选择一种按法
        if random.randint(0, 1) == 0:  # 50%概率选择第一种按法
            print("使用第一种按法")
            # 第一种按法
            touch([1171, 191])
            time.sleep(1)
            touch([1171, 191])
            time.sleep(1)
            touch([1171, 191])
            time.sleep(1)
            time.sleep(5)
            touch([1171, 340])
            time.sleep(1)
            touch([1171, 340])
            time.sleep(1)
            touch([1171, 340])
            time.sleep(1)
            touch(Template(r"tpl1746544552314.png", record_pos=(0.355, 0.18), resolution=(1280, 720)))
            
        else:  # 50%概率选择第二种按法
            print("使用第二种按法")
            # 第二种按法
            touch([1171, 191])
            time.sleep(1)
            time.sleep(5)
            touch([1171, 340])
            time.sleep(1)
            time.sleep(5)
            touch([1171, 490])
            time.sleep(1)
            touch([1171, 490])
            time.sleep(1)  
            touch([1171, 490])
            time.sleep(1)  
            touch([1171, 490])
            time.sleep(1)
            touch(Template(r"tpl1746544552314.png", record_pos=(0.355, 0.18), resolution=(1280, 720)))
        
        # 每次循环后可以加一个随机间隔，使操作更自然
        time.sleep(random.uniform(0.5, 2.0))

        
# 打包零食()