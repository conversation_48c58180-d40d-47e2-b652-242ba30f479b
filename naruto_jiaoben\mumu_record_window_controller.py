#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MuMu操作录制窗口控制器
控制独立的"MuMu 操作录制"窗口
"""

import time
import win32gui
import win32con
import win32api
import win32clipboard
from ctypes import windll

class MuMuRecordController:
    """MuMu操作录制窗口控制器"""
    
    def __init__(self):
        self.record_window = None
        self.find_record_window()
    
    def find_record_window(self):
        """查找MuMu操作录制窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                
                # 查找包含"操作录制"、"录制"、"macro"等关键词的窗口
                keywords = ['操作录制', '录制', 'macro', 'record', 'mumu']
                if any(keyword in window_title.lower() for keyword in keywords):
                    windows.append((hwnd, window_title, class_name))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            print("找到可能的录制窗口:")
            for i, (hwnd, title, class_name) in enumerate(windows):
                print(f"  {i+1}. 标题: {title}")
                print(f"      类名: {class_name}")
                print(f"      句柄: {hwnd}")
                print()
            
            # 让用户选择正确的窗口
            if len(windows) == 1:
                self.record_window = windows[0][0]
                print(f"✅ 自动选择窗口: {windows[0][1]}")
                return True
            else:
                try:
                    choice = input(f"请选择正确的录制窗口 (1-{len(windows)}): ").strip()
                    index = int(choice) - 1
                    if 0 <= index < len(windows):
                        self.record_window = windows[index][0]
                        print(f"✅ 选择窗口: {windows[index][1]}")
                        return True
                    else:
                        print("❌ 无效选择")
                        return False
                except ValueError:
                    print("❌ 输入无效")
                    return False
        else:
            print("❌ 未找到MuMu操作录制窗口")
            print("请确保:")
            print("1. MuMu模拟器已启动")
            print("2. 操作录制窗口已打开")
            return False
    
    def get_window_info(self):
        """获取窗口详细信息"""
        if not self.record_window:
            return None
        
        try:
            # 获取窗口位置和大小
            rect = win32gui.GetWindowRect(self.record_window)
            title = win32gui.GetWindowText(self.record_window)
            class_name = win32gui.GetClassName(self.record_window)
            
            info = {
                'title': title,
                'class_name': class_name,
                'rect': rect,
                'width': rect[2] - rect[0],
                'height': rect[3] - rect[1]
            }
            
            print(f"窗口信息:")
            print(f"  标题: {info['title']}")
            print(f"  类名: {info['class_name']}")
            print(f"  位置: ({rect[0]}, {rect[1]}) - ({rect[2]}, {rect[3]})")
            print(f"  大小: {info['width']} x {info['height']}")
            
            return info
            
        except Exception as e:
            print(f"获取窗口信息失败: {e}")
            return None
    
    def click_button(self, button_text):
        """点击窗口中的按钮"""
        if not self.record_window:
            print("❌ 录制窗口未找到")
            return False
        
        try:
            # 激活窗口
            win32gui.SetForegroundWindow(self.record_window)
            time.sleep(0.2)
            
            # 获取窗口位置
            rect = win32gui.GetWindowRect(self.record_window)
            
            # 根据按钮文本确定点击位置（需要根据实际界面调整）
            button_positions = {
                '操作录制': (rect[0] + 50, rect[1] + 50),
                '开始录制': (rect[0] + 50, rect[1] + 100),
                '停止录制': (rect[0] + 50, rect[1] + 150),
                '播放': (rect[0] + 100, rect[1] + 50),
                '停止': (rect[0] + 150, rect[1] + 50),
                '循环': (rect[0] + 200, rect[1] + 50),
            }
            
            if button_text in button_positions:
                x, y = button_positions[button_text]
                
                # 点击按钮
                win32api.SetCursorPos((x, y))
                time.sleep(0.1)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
                time.sleep(0.05)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x, y, 0, 0)
                
                print(f"✅ 已点击按钮: {button_text}")
                return True
            else:
                print(f"❌ 未知按钮: {button_text}")
                return False
                
        except Exception as e:
            print(f"点击按钮失败: {e}")
            return False
    
    def load_macro_file(self, macro_file):
        """加载宏文件"""
        if not self.record_window:
            print("❌ 录制窗口未找到")
            return False
        
        try:
            # 激活窗口
            win32gui.SetForegroundWindow(self.record_window)
            time.sleep(0.2)
            
            # 方法1：尝试拖拽文件到窗口
            print(f"尝试加载宏文件: {macro_file}")
            
            # 方法2：使用剪贴板和快捷键
            # 复制文件路径到剪贴板
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(macro_file)
            win32clipboard.CloseClipboard()
            
            # 尝试Ctrl+O打开文件
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(ord('O'), 0, 0, 0)
            time.sleep(0.05)
            win32api.keybd_event(ord('O'), 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            time.sleep(1)
            
            # 粘贴文件路径
            win32api.keybd_event(win32con.VK_CONTROL, 0, 0, 0)
            win32api.keybd_event(ord('V'), 0, 0, 0)
            time.sleep(0.05)
            win32api.keybd_event(ord('V'), 0, win32con.KEYEVENTF_KEYUP, 0)
            win32api.keybd_event(win32con.VK_CONTROL, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            time.sleep(0.5)
            
            # 按回车确认
            win32api.keybd_event(win32con.VK_RETURN, 0, 0, 0)
            time.sleep(0.05)
            win32api.keybd_event(win32con.VK_RETURN, 0, win32con.KEYEVENTF_KEYUP, 0)
            
            print("✅ 尝试加载宏文件完成")
            return True
            
        except Exception as e:
            print(f"加载宏文件失败: {e}")
            return False
    
    def start_macro_playback(self, loop=True):
        """开始宏播放"""
        print("开始宏播放...")
        
        # 如果需要循环播放，先设置循环
        if loop:
            print("设置循环播放...")
            self.click_button('循环')
            time.sleep(0.5)
        
        # 开始播放
        self.click_button('播放')
        print("✅ 宏播放已启动")
        return True
    
    def stop_macro_playback(self):
        """停止宏播放"""
        print("停止宏播放...")
        self.click_button('停止')
        print("✅ 宏播放已停止")
        return True
    
    def run_macro_for_duration(self, macro_file, duration_minutes=40):
        """运行宏指定时长"""
        print(f"运行宏 {duration_minutes} 分钟...")
        
        # 1. 加载宏文件
        if not self.load_macro_file(macro_file):
            print("❌ 加载宏文件失败")
            return False
        
        time.sleep(2)
        
        # 2. 开始播放
        if not self.start_macro_playback(loop=True):
            print("❌ 启动宏播放失败")
            return False
        
        # 3. 等待指定时间
        print(f"宏播放中，等待 {duration_minutes} 分钟...")
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration_minutes * 60:
                elapsed = (time.time() - start_time) / 60
                remaining = duration_minutes - elapsed
                print(f"已运行 {elapsed:.1f} 分钟，剩余 {remaining:.1f} 分钟")
                time.sleep(60)  # 每分钟报告一次
        except KeyboardInterrupt:
            print("用户中断播放")
        
        # 4. 停止播放
        self.stop_macro_playback()
        print("✅ 宏运行完成")
        return True

def main():
    print("MuMu操作录制窗口控制器")
    print("=" * 40)
    
    controller = MuMuRecordController()
    
    if not controller.record_window:
        print("请先打开MuMu操作录制窗口，然后重新运行此程序")
        return
    
    # 显示窗口信息
    controller.get_window_info()
    
    print("\n选择操作:")
    print("1. 加载宏文件")
    print("2. 开始播放")
    print("3. 停止播放")
    print("4. 运行40分钟")
    print("5. 自定义运行时间")
    
    choice = input("请选择 (1-5): ").strip()
    
    macro_file = r"E:\learningjiaoben\xuexiyong\jiaoben\naruto_jiaoben\mumu周胜9.10最新.mmor"
    
    if choice == "1":
        controller.load_macro_file(macro_file)
    elif choice == "2":
        controller.start_macro_playback(loop=True)
    elif choice == "3":
        controller.stop_macro_playback()
    elif choice == "4":
        controller.run_macro_for_duration(macro_file, 40)
    elif choice == "5":
        try:
            duration = int(input("运行时长(分钟): "))
            controller.run_macro_for_duration(macro_file, duration)
        except ValueError:
            print("❌ 输入无效")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
