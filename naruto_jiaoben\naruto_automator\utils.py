import time
from core import screen, vision, controller, settings
from naruto_automator.states import state_checker


def wait_for_template(template_name, timeout=None, threshold=0.8, interval=1.0):
    """
    在指定时间内，循环等待某个模板图片出现在屏幕上。
    优化版本：减少不必要的打印输出，使用更短的检查间隔。

    :param template_name: 在assets文件夹中的模板图片文件名。
    :param timeout: 超时时间（秒）。如果为None，则使用配置文件中的默认超时时间。
    :param threshold: 匹配阈值。
    :param interval: 每次检查之间的间隔时间（秒）。
    :return: 如果找到，返回中心点坐标 (x, y)；如果超时，则抛出TimeoutError。
    """
    if timeout is None:
        timeout = settings.DEFAULT_TIMEOUT

    start_time = time.time()
    # 减少日志输出频率，避免UI卡顿
    last_log_time = start_time
    log_interval = 2.0  # 每2秒输出一次等待日志

    print(f"  - 等待图片 '{template_name}' 出现 (超时: {timeout}s)...")

    while True:
        # 检查超时（移到循环开始，确保即使有异常也能超时）
        elapsed_time = time.time() - start_time
        if elapsed_time >= timeout:
            raise TimeoutError(f"等待图片 '{template_name}' 超时 ({timeout}s)。")

        try:
            current_screen = screen.get_screenshot()
            pos = vision.find_template(current_screen, template_name, threshold=threshold)

            if pos:
                print(f"  - 成功找到 '{template_name}'。")
                return pos

            # 减少日志输出频率
            if elapsed_time - (last_log_time - start_time) >= log_interval:
                print(f"  - 继续等待 '{template_name}' ({elapsed_time:.1f}s/{timeout}s)...")
                last_log_time = time.time()

        except Exception as e:
            print(f"  - 等待模板时发生错误: {e}")
            # 对于致命错误（如文件不存在），仍然要检查超时

        # 使用更短的间隔提高响应性，但不要太频繁
        time.sleep(min(interval, 0.5))

def wait_and_tap_template(template_name, timeout=None, threshold=0.8, interval=1.0):
    """
    等待某个模板图片出现，并点击它的中心位置。
    这是一个常用操作的组合。

    :param template_name: 模板图片文件名。
    :param timeout: 超时时间（秒）。
    :param threshold: 匹配阈值。
    :param interval: 检查间隔（秒）。
    :return: 点击的坐标 (x, y)。如果超时则抛出TimeoutError。
    """
    pos = wait_for_template(template_name, timeout, threshold, interval)
    controller.tap(pos[0], pos[1])
    return pos

def swipe_and_find_template(template_name, max_swipes=10, swipe_duration=500, threshold=0.8):
    """
    通过滑动查找目标图片，找到后点击它。
    这是您原始代码中滑动查找逻辑的通用版本。

    :param template_name: 要查找的模板图片文件名。
    :param max_swipes: 最大滑动次数。
    :param swipe_duration: 滑动持续时间（毫秒）。
    :param threshold: 匹配阈值。
    :return: 如果找到并点击成功，返回True；否则返回False。
    """
    wait_and_tap_template("tpl1744699918742.png")
    print("  -> 已点击功能入口")
    print(f"  -> 开始滑动查找图片 '{template_name}'...")
    
    for swipe_count in range(max_swipes):
        # 先滑动
        controller.swipe(1024, 360, 256, 360, swipe_duration)
        time.sleep(1)
        
        # 滑动后检查是否找到目标图片
        current_screen = screen.get_screenshot()
        pos = vision.find_template(current_screen, template_name, threshold=threshold)
        
        if pos:
            print(f"  -> 找到目标图片 '{template_name}'，滑动次数: {swipe_count + 1}")
            # 找到后点击它
            controller.tap(pos[0], pos[1]+214)
            print(f"  -> 已点击目标图片 '{template_name}'")
            return True
    
    print(f"  -> [错误] 滑动 {max_swipes} 次后未找到图片 '{template_name}'")
    return False

def tap_template(template_name, threshold=0.8):
    """
    查找模板图片并点击它的中心位置（不等待）

    :param template_name: 模板图片文件名
    :param threshold: 匹配阈值
    :return: 如果找到并点击成功返回坐标，否则返回None
    """
    try:
        current_screen = screen.get_screenshot()
        pos = vision.find_template(current_screen, template_name, threshold=threshold)
        if pos:
            controller.tap(pos[0], pos[1])
            return pos
        else:
            print(f"  - 未找到模板图片 '{template_name}'")
            return None
    except Exception as e:
        print(f"  - 点击模板时发生错误: {e}")
        return None

def handle_timeout_and_return_to_main(max_attempts=10):
    """
    通用的超时错误处理函数。
    当发生超时后，会尝试通过不断点击返回/关闭按钮，直到返回主菜单。

    :param max_attempts: 最大尝试次数，防止无限循环。
    :return: 如果成功返回主菜单，返回 True；否则返回 False。
    """
    print("  -> [恢复模式] 启动超时错误恢复程序...")
    close_button_template = "tpl1721210501833.png" # 你指定的通用关闭/返回按钮

    for i in range(max_attempts):
        print(f"  -> [恢复模式] 第 {i + 1}/{max_attempts} 次尝试...")
        
        # 步骤1: 检查是否已经意外地回到了主菜单
        if state_checker.is_in_main_menu():
            print("  -> [恢复模式] 检测到已在主菜单，恢复成功！")
            return True

        # 步骤2: 循环点击关闭按钮，直到它消失
        # 这个内部循环确保了即使需要点很多次关闭按钮，也能处理
        while True:
            current_screen = screen.get_screenshot()
            close_button_pos = vision.find_template(current_screen, close_button_template)
            
            if close_button_pos:
                print(f"  -> [恢复模式] 找到关闭按钮 '{close_button_template}'，点击它...")
                controller.tap(close_button_pos[0], close_button_pos[1])
                time.sleep(1.5) # 点击后等待一下，让UI有反应时间
            else:
                # 屏幕上找不到关闭按钮了，跳出内部循环
                print(f"  -> [恢复模式] 关闭按钮 '{close_button_template}' 已消失或不存在。")
                break
        
        # 步骤3: 在点击完关闭按钮后，再次检查是否已回到主菜单
        time.sleep(1) # 等待界面稳定
        if state_checker.is_in_main_menu():
            print("  -> [恢复模式] 已成功返回主菜单！")
            return True
            
    print("  -> [恢复模式] 尝试多次后仍无法返回主菜单，恢复失败。")
    return False

# 你可以在这里添加更多有用的工具函数，例如:
# - 等待某个图片从屏幕上消失
# - 结合OCR和等待，等待某个特定的文字出现 
