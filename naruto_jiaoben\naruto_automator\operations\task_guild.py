import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_task_guild():
    """执行任务集会所任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行任务集会所任务...")
        
        # 使用通用滑动查找函数查找任务集会所功能
        if not utils.swipe_and_find_template("tpl1744774678712.png"):
            print("  -> [错误] 未找到任务集会所功能")
            return False
        
        # 等待任务集会所界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744774696751.png")
        print("  -> 任务集会所界面已加载")
        
 
        controller.tap(655, 166)
        print("  -> 领取奖励")
        utils.wait_for_template("tpl1744774832008.png")
        print("  -> 确认提示")

        utils.wait_and_tap_template("tpl1744774841607.png")
        print("  -> 确定领取")
        controller.tap(1182, 452)
        time.sleep(1)
        controller.tap(1182, 452)
 
        
        time.sleep(10)  # 等待任务开始
        
        # 点击第1个任务位置
        controller.tap(1168, 271)
        print("  -> 点击第1个任务位置")
        time.sleep(10)
        # 检查是否有确认对话框
        current_screen = screen.get_screenshot()
        if vision.find_template(current_screen, "tpl1744943660413.png"):
            utils.wait_and_tap_template("tpl1744943706232.png")
            print("  -> 已确认第1个任务")
        else:
            controller.tap(1182, 452)
            print("  -> 直接开始第1个任务")
        
        # 等待任务完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744775133750.png")
        print("  -> 第1个任务完成")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744775133750.png")
        print("  -> 已确认第1个任务完成")
        
        time.sleep(5)  # 等待界面刷新
        
        # 等待任务集会所界面重新加载（只等待，不点击）
        utils.wait_for_template("tpl1744774696751.png")
        print("  -> 任务集会所界面重新加载")
        
        # 点击第2个任务位置
        controller.tap(1168, 390)
        print("  -> 点击第2个任务位置")
        time.sleep(10)
        # 检查是否有确认对话框
        current_screen = screen.get_screenshot()
        if vision.find_template(current_screen, "tpl1744943660413.png"):
            utils.wait_and_tap_template("tpl1744943706232.png")
            print("  -> 已确认第2个任务")
        else:
            controller.tap(1182, 452)
            print("  -> 直接开始第2个任务")
        
        # 等待任务完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744775133750.png")
        print("  -> 第2个任务完成")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744775133750.png")
        print("  -> 已确认第2个任务完成")
        
        time.sleep(5)  # 等待界面
        
        # 等待任务集会所界面重新加载（只等待，不点击）
        utils.wait_for_template("tpl1744774696751.png")
        print("  -> 任务集会所界面重新加载")
        
        # 点击第3个任务位置
        controller.tap(1168, 493)
        print("  -> 点击第3个任务位置")
        time.sleep(10)
        # 检查是否有确认对话框
        current_screen = screen.get_screenshot()
        if vision.find_template(current_screen, "tpl1744943660413.png"):
            utils.wait_and_tap_template("tpl1744943706232.png")
            print("  -> 已确认第3个任务")
        else:
            controller.tap(1182, 452)
            print("  -> 直接开始第3个任务")
        
        # 等待任务完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744775133750.png")
        print("  -> 第3个任务完成")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744775133750.png")
        print("  -> 已确认第3个任务完成")
        
        time.sleep(6)  # 等待界面刷新
        
        # 等待返回按钮出现（只等待，不点击）
        utils.wait_for_template("tpl1744775700414.png")
        print("  -> 返回按钮已出现")
        
        # 点击返回按钮
        utils.wait_and_tap_template("tpl1744775719622.png")
        print("  -> 已点击返回按钮")
        
        print("  -> 任务集会所任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc() 