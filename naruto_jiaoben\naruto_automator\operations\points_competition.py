import time
import re
from naruto_automator import utils
from core import controller, screen, vision
from core.ocr_system import ocr_to_int, init_ocr_system, ocr_text
from naruto_automator.states import state_checker

def get_challenge_count():
    """
    使用OCR检测挑战次数
    返回 (当前次数, 最大次数) 或 None
    """
    try:
        current_screen = screen.get_screenshot()

        # 使用精确的坐标位置（基于实际检测结果）
        regions = [
            (329, 629, 171, 33),  # 精确位置：基于实际检测坐标
            (320, 620, 190, 50),  # 稍微扩大的区域
            (310, 610, 210, 60),  # 更大的安全区域
        ]

        for i, region in enumerate(regions):
            challenge_text = ocr_text(current_screen, region=region)

            if challenge_text:
                print(f"  -> 区域{i+1} OCR识别到文本: '{challenge_text}'")

                # 匹配 "挑战次数: 0/6" 或 "0/6" 格式
                patterns = [
                    r'挑战次数[：:]\s*(\d+)/(\d+)',  # 完整格式
                    r'(\d+)/(\d+)',                    # 简化格式
                    r'挑战.*?(\d+).*?(\d+)',          # 模糊匹配
                    r'次数.*?(\d+).*?(\d+)',          # 包含"次数"的匹配
                ]

                for pattern in patterns:
                    match = re.search(pattern, challenge_text)
                    if match:
                        remaining = int(match.group(1))  # 第一个数字是剩余次数
                        total = int(match.group(2))      # 第二个数字是总次数
                        print(f"  -> 检测到挑战次数: 剩余{remaining}次/总共{total}次")
                        return remaining, total

        print("  -> 所有区域都未能识别挑战次数文本")
        return None

    except Exception as e:
        print(f"  -> 挑战次数检测失败: {e}")
        return None

def run_points_competition():
    """执行积分赛任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行积分赛任务...")
        
        # 初始化OCR系统
        print("  -> 初始化OCR系统...")
        if not init_ocr_system("paddle"):
            print("  -> [警告] OCR系统初始化失败，将使用模拟数据")
        
        # 使用通用滑动查找函数查找积分赛功能
        if not utils.swipe_and_find_template("tpl1745475991391.png"):
            print("  -> [错误] 未找到积分赛功能")
            return False
        
        time.sleep(10)  # 等待界面加载
        
        # 点击开始按钮
        controller.tap(666, 524)
        time.sleep(5)
        controller.tap(666, 524)
        time.sleep(5)
        controller.tap(666, 524)
        time.sleep(5)
        
        # 等待积分赛界面加载（只等待，不点击）
        utils.wait_for_template("tpl1745998709035.png")
        print("  -> 积分赛界面已加载")
        
        # 主循环部分（使用OCR检测次数）
        execution_count = 0
        max_attempts = 10  # 最大尝试次数，防止无限循环

        while execution_count < max_attempts:
            # 检测当前挑战次数
            challenge_info = get_challenge_count()

            if challenge_info:
                remaining_count, total_count = challenge_info
                print(f"  -> 当前挑战状态: 剩余{remaining_count}次/总共{total_count}次")

                # 如果剩余次数为0，退出循环
                if remaining_count <= 0:
                    print(f"  -> 挑战次数已用完 (剩余{remaining_count}次)，任务结束")
                    break

                completed_count = total_count - remaining_count
                print(f"  -> 开始第{completed_count + 1}次积分赛...")
            else:
                # 如果OCR检测失败，使用备用计数
                print(f"  -> OCR检测失败，使用备用计数 - 第{execution_count + 1}次积分赛...")
                if execution_count >= 6:  # 备用最大次数
                    print("  -> 已达到备用最大次数，任务结束")
                    break
            
            # 战力检测部分
            time.sleep(10)
            utils.wait_for_template("tpl1745476047512.png")
            print("  -> 战力检测界面已加载")
            time.sleep(10)
            # 点击战力检测按钮
            utils.wait_and_tap_template("tpl1745476068545.png")
            print("  -> 已点击战力检测按钮")
            
            time.sleep(10)  # 等待战力检测完成

            # 战力检测循环
            while True:
                # 等待战力显示界面
                utils.wait_for_template("tpl1745637697775.png")
                print("  -> 战力显示界面已加载")
                
                # 点击我的战力按钮
                utils.wait_and_tap_template("tpl1745637731371.png")
                print("  -> 已点击我的战力按钮")
                
                # 获取当前屏幕截图
                current_screen = screen.get_screenshot()
                
                # 我的战力
                numberme = ocr_to_int(current_screen, region=(271, 329, 86, 36))
                if numberme is None:
                    print("  -> [警告] 无法识别我的战力，使用默认值10000")
                    numberme = 10000
                print(f"  -> 第{execution_count+1}次执行 - 我的战力: {numberme}")
                
                # 其他战力检测
                number1 = ocr_to_int(current_screen, region=(840, 234, 114, 30))
                number2 = ocr_to_int(current_screen, region=(843, 353, 113, 29))
                number3 = ocr_to_int(current_screen, region=(839, 469, 117, 33))
                number4 = ocr_to_int(current_screen, region=(842, 587, 117, 31))
                
                # 创建数字字典（过滤无效值）
                valid_numbers = {
                    num: pos for num, pos in zip(
                        [number1, number2, number3, number4],
                        [[1032, 203], [1032, 320], [1032, 435], [1032, 562]]
                    ) if num is not None
                }
                
                if not valid_numbers:
                    print("  -> 战力识别失败，重试...")
                    time.sleep(2)
                    continue
                
                min_number = min(valid_numbers.keys())
                min_position = valid_numbers[min_number]
                
                if min_number < numberme + 500:
                    print(f"  -> 选择战力{min_number}的对手")
                    controller.tap(min_position[0], min_position[1])
                    time.sleep(15)
                    
                    # 等待战斗界面
                    utils.wait_for_template("tpl1745480697856.png",timeout=60)
                    print("  -> 战斗界面已加载")
                    
                    # 点击战斗按钮
                    utils.wait_and_tap_template("tpl1745480697856.png")
                    print("  -> 已点击战斗按钮")
                    
                    # 等待战斗结果界面
                    utils.wait_for_template("tpl1745478141734.png", timeout=60)
                    print("  -> 战斗结果界面已加载")
                    
                    # 点击确认按钮
                    utils.wait_and_tap_template("tpl1745478141734.png", timeout=60)
                    print("  -> 已点击确认按钮")
                    
                    time.sleep(20)
                    controller.tap(666, 524)
                    time.sleep(20)
                    execution_count += 1

                    # 再次检测挑战次数确认完成
                    time.sleep(3)  # 等待界面更新
                    updated_info = get_challenge_count()
                    if updated_info:
                        remaining, total = updated_info
                        print(f"  -> 积分赛完成，当前状态: 剩余{remaining}次/总共{total}次")
                    else:
                        print(f"  -> 第{execution_count}次积分赛完成")

                    break  # 跳出战力检测循环，继续主循环
                else:
                    print("  -> 没有合适对手，刷新后重试...")
                    utils.wait_and_tap_template("tpl1745477729006.png")
                    time.sleep(5)
                    # 自动跳转回战力检测部分（通过内层while True循环）
        
        print("  -> 积分赛任务完成")
        
        # 等待最终界面
        utils.wait_for_template("tpl1745481348134.png")
        print("  -> 最终界面已加载")
        
        # 点击返回按钮
        utils.wait_and_tap_template("tpl1745481363805.png")
        print("  -> 已点击返回按钮")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

# 向后兼容的包装函数
def run():
    """标准的run函数"""
    return run_points_competition()