from core import screen, vision

# --- 状态检查函数 ---
# 所有的状态检查函数都应该遵循同样的模式:
# 1. 获取最新屏幕截图。
# 2. 使用 vision.find_template 寻找该状态下的特征图片。
# 3. 返回 True 或 False。

def is_in_main_menu():
    """
    判断当前是否在游戏主菜单界面。
    通过寻找一个主菜单才有的、独一无二的元素来判断。
    """
    print("  - 状态检查: 是否在主菜单?")
    # 使用你指定的 tpl1744706931346.png 作为主菜单的判断依据
    template_name = "tpl1744706931346.png"
    
    current_screen = screen.get_screenshot()
    pos = vision.find_template(current_screen, template_name, threshold=0.85) # 主界面图标通常比较固定，可以用稍高精度
    
    return pos is not None

def is_in_battle():
    """
    判断当前是否在战斗界面。
    通过寻找战斗界面才有的元素来判断，例如"自动战斗"按钮或血条。
    """
    print("  - 状态检查: 是否在战斗中?")
    # 注意：'battle_auto_button.png' 是一个示例名称，你需要替换成你自己的资源图片名
    template_name = "battle_auto_button.png"

    current_screen = screen.get_screenshot()
    pos = vision.find_template(current_screen, template_name, threshold=0.85) # 战斗按钮可能需要更高精度
    
    return pos is not None
    
def is_on_map():
    """
    判断当前是否在大地图/关卡选择界面。
    """
    print("  - 状态检查: 是否在地图界面?")
    # 注意：'map_indicator.png' 是一个示例名称，你需要替换成你自己的资源图片名
    template_name = "map_indicator.png"
    
    current_screen = screen.get_screenshot()
    pos = vision.find_template(current_screen, template_name)
    
    return pos is not None




# ... 在这里可以继续添加更多的状态检查函数 ...
# 例如： is_in_shop(), is_in_team_setup() 等 