import time
from core import controller, screen, vision
from naruto_automator.states import state_checker
from naruto_automator import utils

def run():
    """
    执行"领取奖励"任务的主函数。
    基于用户原有的领取奖励逻辑重写。
    """
    print("  -> 开始'领取奖励'任务流程")

    # 任务开始前：检查是否在主菜单
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return

    try:
        # 基于用户原有逻辑的领取奖励流程
        _collect_rewards_original()

        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()

    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

    print("  -> '领取奖励'任务流程结束")


def _collect_rewards_original():
    """
    基于用户原有逻辑的领取奖励函数
    """
    print("  -> 开始执行领取奖励流程...")
    
    # 1. 点击主菜单按钮
    utils.wait_and_tap_template("tpl1744699918742.png", timeout=10)
    print("  -> 已点击主菜单按钮")
    time.sleep(6)
    
    # 2. 依次点击横向分布的按钮
    horizontal_buttons = [
        (513, 563),  # 最左侧按钮
        (609, 589),
        (739, 559),  # 左中按钮
        (609, 585),
        (1034, 569), # 右中按钮
        (609, 583),
        (1184, 563), # 最右侧按钮
        (609, 581),
    ]
    
    print("  -> 开始点击横向按钮...")
    for i, btn in enumerate(horizontal_buttons):
        controller.tap(btn[0], btn[1])
        print(f"  -> 点击按钮 {i+1}: ({btn[0]}, {btn[1]})")
        time.sleep(1)  # 每次点击间隔1秒
    
    # 3. 最终等待和确认
    time.sleep(10)
    utils.wait_and_tap_template("tpl1744780400328.png", timeout=10)
    print("  -> 已点击确认按钮")
    
    # 这里只有wait，没有touch，所以只等待
    utils.wait_for_template("tpl1744780427446.png", timeout=10)
    print("  -> 等待第二个确认按钮出现")
    
    utils.wait_and_tap_template("tpl1744780455793.png", timeout=10)
    print("  -> 点击红房子")
    
    time.sleep(20)
    
    # 4. 处理奖励界面 - 这里也是只有wait，没有touch
    utils.wait_for_template("tpl1744780501625.png", timeout=10)
    print("  -> 等待奖励界面出现")
    
    utils.wait_and_tap_template("tpl1744780524297.png", timeout=10)
    print("  -> 已点击奖励确认")
    
    time.sleep(10)
    
    # 5. 处理特殊弹窗
    # 检测特殊弹窗，如果存在就处理，否则点击备用按钮
    if utils.tap_template("tpl1744780547913.png"):
        print("  -> 检测到特殊弹窗")
        utils.wait_and_tap_template("tpl1744780558065.png", timeout=15)
        print("  -> 已处理特殊弹窗")
    else:
        controller.tap(622, 90)
        print("  -> 未检测到特殊弹窗，已点击备用按钮")
        utils.wait_and_tap_template("tpl1744780558065.png", timeout=15)
        time.sleep(3)
    
    time.sleep(5)
    
    # 6. 处理邮件奖励 - 这里也是只有wait，没有touch
    utils.wait_for_template("tpl1744866390107.png", timeout=10)
    print("  -> 等待邮件界面出现")
    
    utils.wait_and_tap_template("tpl1744866412537.png", timeout=10)
    print("  -> 已点击邮件确认")
    
    time.sleep(10)
    
    # 7. 循环领取邮件奖励
    print("  -> 开始循环领取邮件奖励...")
    target = "tpl1745554121705.png"
    while True:
        try:
            pos = utils.wait_for_template(target, timeout=3)
            if pos:
                controller.tap(pos[0], pos[1])
                print("  -> 已点击邮件奖励")
                time.sleep(10)
        except TimeoutError:
            print("  -> 邮件奖励领取完成")
            break
    
    # 8. 返回主界面
    utils.wait_and_tap_template("tpl1744780862904.png", timeout=10)
    print("  -> 已点击返回按钮")
    
    # 这里也是只有wait，没有touch
    utils.wait_for_template("tpl1744781523417.png", timeout=10)
    print("  -> 等待返回确认界面出现")
    
    utils.wait_and_tap_template("tpl1744781529825.png", timeout=10)
    print("  -> 已点击返回确认")
    
    time.sleep(20)
    
    # 9. 最终确认和返回
    utils.wait_and_tap_template("tpl1744781578861.png", timeout=10)
    print("  -> 已点击最终确认")
    
    controller.tap(718, 144)
    print("  -> 已点击屏幕中心")
    
    time.sleep(6)
    
    # 10. 处理活动奖励
    utils.wait_and_tap_template("tpl1744780920850.png", timeout=10)
    print("  -> 已进入活动界面")
    
    time.sleep(30)
    
    # 这里也是只有wait，没有touch
    utils.wait_for_template("tpl1744781648648.png", timeout=10)
    print("  -> 等待活动奖励出现")
    
    controller.tap(59, 276)
    print("  -> 已点击活动奖励")
    
    time.sleep(5)
    
    utils.wait_and_tap_template("tpl1744781699248.png", timeout=10)
    print("  -> 已确认活动奖励")
    
    time.sleep(10)
    
    utils.wait_and_tap_template("tpl1744781721912.png", timeout=10)
    print("  -> 已返回主界面")
    
    print("  -> 领取奖励流程完成") 