import yaml
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.parent

# 配置文件路径
CONFIG_FILE = ROOT_DIR / "config.yaml"

def load_config():
    """加载YAML配置文件"""
    if not CONFIG_FILE.exists():
        raise FileNotFoundError(f"配置文件未找到: {CONFIG_FILE}")
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

# 加载配置
config = load_config()

# 导出常用配置
COMMON_CONFIG = config.get('common', {})
ADB_PATH = COMMON_CONFIG.get('adb_path', 'adb')
DEVICE_URI = COMMON_CONFIG.get('device_uri')
DEFAULT_TIMEOUT = COMMON_CONFIG.get('default_timeout', 10)

# OCR配置
OCR_CONFIG = config.get('ocr', {})
OCR_TYPE = OCR_CONFIG.get('type', 'onnx')
OCR_THRESHOLD = OCR_CONFIG.get('threshold', 0.5)
OCR_AUTO_INIT = OCR_CONFIG.get('auto_init', True)

TASKS_CONFIG = config.get('tasks', {})

if not DEVICE_URI:
    raise ValueError("请在 config.yaml 中配置你的设备URI (device_uri)")

print("配置加载成功:")
print(f"  - ADB路径: {ADB_PATH}")
print(f"  - 设备地址: {DEVICE_URI}")
print(f"  - OCR类型: {OCR_TYPE}")
print(f"  - OCR阈值: {OCR_THRESHOLD}") 