#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Alt+S 快捷键功能
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_alt_s_hotkey():
    """测试 Alt+S 快捷键发送功能"""
    try:
        from mumu_macro_flow_controller import MuMuMacroFlowController
        
        print("=" * 60)
        print("测试 Alt+S 快捷键功能")
        print("=" * 60)
        
        # 创建控制器
        print("\n1. 初始化 MuMu 控制器...")
        controller = MuMuMacroFlowController()
        
        if not controller.mumu_main_window:
            print("❌ 未找到 MuMu 模拟器窗口")
            print("   请确保 MuMu 模拟器已启动")
            return False
        
        print(f"✅ 找到 MuMu 模拟器窗口")
        print(f"   窗口句柄: {controller.mumu_main_window}")
        
        # 测试窗口级别的 Alt+S 快捷键
        print("\n2. 测试窗口级别的 Alt+S 快捷键...")
        print("   注意：这将向 MuMu 窗口发送 Alt+S 快捷键")
        print("   如果 MuMu 正在录制，这可能会停止录制")
        
        # 给用户5秒时间准备
        for i in range(5, 0, -1):
            print(f"   {i} 秒后发送 Alt+S...")
            time.sleep(1)
        
        # 发送 Alt+S 快捷键
        if controller.send_alt_s_hotkey_to_window(controller.mumu_main_window, "MuMu模拟器12-1"):
            print("✅ Alt+S 快捷键发送成功")
        else:
            print("❌ Alt+S 快捷键发送失败")
            return False
        
        print("\n3. 测试完成")
        print("   请检查 MuMu 模拟器是否响应了 Alt+S 快捷键")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_method_availability():
    """测试方法可用性"""
    try:
        from mumu_macro_flow_controller import MuMuMacroFlowController
        
        print("\n" + "=" * 60)
        print("测试方法可用性")
        print("=" * 60)
        
        # 检查新方法是否存在
        methods_to_check = [
            'send_alt_s_hotkey_to_window',
            'send_alt_p_hotkey',
            'bring_window_to_front',
            'execute_complete_flow'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MuMuMacroFlowController, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法检查失败: {e}")
        return False

def show_usage_info():
    """显示使用信息"""
    print("\n" + "=" * 60)
    print("Alt+S 快捷键功能说明")
    print("=" * 60)
    
    print("\n🎯 功能说明:")
    print("- 在决斗场流程的最后阶段（40分钟后）")
    print("- 不再使用图像识别点击 record_button3.png")
    print("- 改为向 MuMu 窗口发送 Alt+S 快捷键")
    print("- Alt+S 通常用于停止录制")
    
    print("\n🔧 技术实现:")
    print("- 使用 win32api.keybd_event 发送按键")
    print("- 先将 MuMu 窗口置于前台")
    print("- 发送窗口级别的 Alt+S 组合键")
    print("- 如果失败，会尝试全局发送")
    
    print("\n⚙️ 优势:")
    print("- 不依赖图片模板，更稳定")
    print("- 响应速度更快")
    print("- 减少图像识别的复杂性")
    print("- 更符合 MuMu 的操作习惯")
    
    print("\n📋 流程变化:")
    print("原来: 识别 record_button3.png → 点击按钮")
    print("现在: 调出 MuMu 窗口 → 发送 Alt+S 快捷键")

def main():
    """主函数"""
    print("MuMu 宏录制控制器 - Alt+S 快捷键测试")
    
    # 测试方法可用性
    if not test_method_availability():
        return
    
    # 显示使用信息
    show_usage_info()
    
    # 询问是否进行实际测试
    print("\n" + "=" * 60)
    response = input("是否进行实际的 Alt+S 快捷键测试？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n⚠️  警告：这将向 MuMu 窗口发送真实的 Alt+S 快捷键")
        print("   如果 MuMu 正在录制，可能会停止录制")
        confirm = input("确认继续？(y/N): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            success = test_alt_s_hotkey()
            if success:
                print("\n🎉 Alt+S 快捷键功能测试成功！")
            else:
                print("\n❌ Alt+S 快捷键功能测试失败！")
        else:
            print("\n取消实际测试")
    else:
        print("\n跳过实际测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
