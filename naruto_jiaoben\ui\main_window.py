#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火影忍者自动化脚本 - 主窗口UI
"""

import sys
import os
import threading
import time
import signal
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QPushButton, QLabel, QTextEdit, QCheckBox,
    QGroupBox, QTabWidget, QProgressBar, QSpinBox, QComboBox,
    QSplitter, QFrame, QScrollArea, QMessageBox, QFileDialog,
    QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core import settings
from core.ocr_system import init_ocr_system, get_ocr_system
from naruto_automator.operations import (
    adventure, recruit, task_guild, daily_share, equipment_upgrade,
    survival_challenge, abundant_realm, organization_coin, team_raid,
    duel_arena, points_competition, daily_rewards
)

class RealTimeLogHandler:
    """实时日志处理器，用于实时重定向输出到UI"""

    def __init__(self, log_signal):
        self.log_signal = log_signal
        self.buffer = ""

    def write(self, text):
        self.buffer += text
        # 遇到换行符时立即发送日志
        if '\n' in self.buffer:
            lines = self.buffer.split('\n')
            # 处理除最后一个元素外的所有行（最后一个可能是不完整的）
            for line in lines[:-1]:
                if line.strip():  # 只发送非空行
                    self.log_signal.emit(line.strip())
            # 保留最后一个不完整的行
            self.buffer = lines[-1]

    def flush(self):
        # 刷新时发送剩余的缓冲内容
        if self.buffer.strip():
            self.log_signal.emit(self.buffer.strip())
            self.buffer = ""

class TaskRunner(QThread):
    """优化的任务执行线程"""

    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal()

    def __init__(self, tasks_to_run):
        super().__init__()
        self.tasks_to_run = tasks_to_run
        self.is_running = True
        self.start_time = None
        self.log_buffer = []
        self.last_log_emit = time.time()
    
    def run(self):
        """执行任务"""
        old_stdout = None
        self.start_time = time.time()
        try:
            # 设置实时日志重定向
            import sys

            old_stdout = sys.stdout
            # 使用实时日志处理器
            real_time_handler = RealTimeLogHandler(self.log_signal)
            sys.stdout = real_time_handler

            # 初始化OCR系统
            self.log_signal.emit("正在初始化OCR系统...")
            if init_ocr_system(settings.OCR_TYPE):
                self.log_signal.emit("OCR系统初始化成功")
            else:
                self.log_signal.emit("OCR系统初始化失败")

            # 连接设备
            self.log_signal.emit("正在连接设备...")
            if self._connect_device():
                self.log_signal.emit("设备连接成功")
            else:
                self.log_signal.emit("设备连接失败，请检查配置")
                return
            
            # 执行任务
            for i, (task_name, task_func) in enumerate(self.tasks_to_run.items()):
                if not self.is_running:
                    break

                self.status_signal.emit(f"正在执行: {task_name}")
                self.progress_signal.emit(i)
                self.log_signal.emit(f"\n=== 开始执行 {task_name} ===")
                
                try:
                    # 执行任务（输出会通过RealTimeLogHandler实时显示）
                    task_func()

                    self.log_signal.emit(f"=== {task_name} 执行完成 ===")
                except Exception as e:
                    import traceback
                    error_msg = f"=== {task_name} 执行失败: {e} ==="
                    self.log_signal.emit(error_msg)
                    # 输出详细错误信息
                    error_trace = traceback.format_exc()
                    for line in error_trace.split('\n'):
                        if line.strip():
                            self.log_signal.emit(f"  {line.strip()}")
                
                time.sleep(1)
            
            self.progress_signal.emit(len(self.tasks_to_run))
            total_time = time.time() - self.start_time
            self.status_signal.emit(f"所有任务执行完成 (用时: {total_time:.1f}秒)")
            self.log_signal.emit(f"总执行时间: {total_time:.1f}秒")
            
        except Exception as e:
            self.log_signal.emit(f"执行过程中发生错误: {e}")
        finally:
            # 恢复原始stdout
            if old_stdout is not None:
                import sys
                sys.stdout = old_stdout
            self.finished_signal.emit()
    
    def stop(self):
        """停止任务"""
        self.is_running = False
    
    def _connect_device(self):
        """连接设备"""
        try:
            import subprocess
            command = [settings.ADB_PATH, 'connect', settings.DEVICE_URI]
            result = subprocess.run(
                command, 
                check=True, 
                capture_output=True, 
                encoding='utf-8',
                errors='ignore'
            )
            output = result.stdout.lower()
            return "connected" in output or "already connected" in output
        except Exception:
            return False

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.task_runner = None
        self.init_ui()
        self.setup_logging()

        # 设置定时器用于批量更新日志
        self.log_timer = QTimer()
        self.log_timer.timeout.connect(self._flush_log_batch)
        self.log_timer.start(100)  # 每100ms检查一次
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("火影忍者自动化脚本 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
            QCheckBox {
                font-size: 14px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QLabel {
                font-size: 14px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧控制面板
        left_panel = self.create_left_panel()
        main_layout.addWidget(left_panel, 1)
        
        # 右侧日志面板
        right_panel = self.create_right_panel()
        main_layout.addWidget(right_panel, 2)
    
    def create_left_panel(self):
        """创建左侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标题
        title = QLabel("任务控制面板")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #333; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 任务选择组
        task_group = QGroupBox("任务选择")
        task_layout = QGridLayout(task_group)
        
        # 任务复选框
        self.task_checkboxes = {}
        tasks = [
            ("adventure", "冒险副本", adventure.run),
            ("recruit", "招募", recruit.run_recruit),
            ("task_guild", "任务集会所", task_guild.run_task_guild),
            ("daily_share", "每日分享", daily_share.run_daily_share),
            ("survival_challenge", "生存挑战", survival_challenge.run_survival_challenge),
            ("abundant_realm", "丰饶之间", abundant_realm.run_abundant_realm),
            ("organization_coin", "组织祈福", organization_coin.run_organization_coin),
            ("team_raid", "小队突袭", team_raid.run_team_raid),
            ("duel_arena", "决斗场", duel_arena.run),
            ("points_competition", "积分赛", points_competition.run_points_competition),
            ("daily_rewards", "领取奖励", daily_rewards.run),
            ("equipment_upgrade", "升级装备", equipment_upgrade.run),
        ]
        
        for i, (key, name, func) in enumerate(tasks):
            checkbox = QCheckBox(name)
            self.task_checkboxes[key] = (checkbox, func)
            task_layout.addWidget(checkbox, i // 2, i % 2)
        
        layout.addWidget(task_group)
        
        # 控制按钮组
        control_group = QGroupBox("控制")
        control_layout = QVBoxLayout(control_group)
        
        # 开始/停止按钮
        self.start_btn = QPushButton("开始执行")
        self.start_btn.clicked.connect(self.start_tasks)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止执行")
        self.stop_btn.clicked.connect(self.stop_tasks)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        # 任务队列管理
        queue_group = QGroupBox("任务队列")
        queue_layout = QVBoxLayout(queue_group)

        # 队列操作按钮
        queue_btn_layout = QHBoxLayout()

        self.add_to_queue_btn = QPushButton("添加到队列")
        self.add_to_queue_btn.clicked.connect(self.add_selected_to_queue)
        queue_btn_layout.addWidget(self.add_to_queue_btn)

        self.load_default_queue_btn = QPushButton("加载默认队列")
        self.load_default_queue_btn.clicked.connect(self.load_default_queue)
        queue_btn_layout.addWidget(self.load_default_queue_btn)

        self.clear_queue_btn = QPushButton("清空队列")
        self.clear_queue_btn.clicked.connect(self.clear_queue)
        queue_btn_layout.addWidget(self.clear_queue_btn)

        self.save_queue_btn = QPushButton("保存为默认")
        self.save_queue_btn.clicked.connect(self.save_current_queue_as_default)
        queue_btn_layout.addWidget(self.save_queue_btn)

        queue_layout.addLayout(queue_btn_layout)

        # 队列显示和操作区域
        queue_display_layout = QHBoxLayout()

        # 队列显示列表
        self.queue_list = QListWidget()
        self.queue_list.setMaximumHeight(120)
        self.queue_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        queue_display_layout.addWidget(self.queue_list)

        # 队列操作按钮（垂直布局）
        queue_ops_layout = QVBoxLayout()

        self.move_up_btn = QPushButton("↑")
        self.move_up_btn.setMaximumWidth(30)
        self.move_up_btn.setToolTip("上移选中任务")
        self.move_up_btn.clicked.connect(self.move_task_up)
        queue_ops_layout.addWidget(self.move_up_btn)

        self.move_down_btn = QPushButton("↓")
        self.move_down_btn.setMaximumWidth(30)
        self.move_down_btn.setToolTip("下移选中任务")
        self.move_down_btn.clicked.connect(self.move_task_down)
        queue_ops_layout.addWidget(self.move_down_btn)

        self.remove_task_btn = QPushButton("✕")
        self.remove_task_btn.setMaximumWidth(30)
        self.remove_task_btn.setToolTip("删除选中任务")
        self.remove_task_btn.clicked.connect(self.remove_selected_task)
        queue_ops_layout.addWidget(self.remove_task_btn)

        queue_ops_layout.addStretch()  # 添加弹性空间
        queue_display_layout.addLayout(queue_ops_layout)

        queue_layout.addLayout(queue_display_layout)

        # 队列执行按钮
        self.execute_queue_btn = QPushButton("执行队列")
        self.execute_queue_btn.clicked.connect(self.execute_queue)
        self.execute_queue_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        queue_layout.addWidget(self.execute_queue_btn)

        control_layout.addWidget(queue_group)

        # 初始化任务队列
        self.task_queue = []

        # 连接队列列表选择变化信号
        self.queue_list.currentRowChanged.connect(self._update_queue_operation_buttons)
        layout.addWidget(control_group)
        
        # 状态显示组
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_group)
        
        # 配置组
        config_group = QGroupBox("配置")
        config_layout = QGridLayout(config_group)
        
        config_layout.addWidget(QLabel("OCR类型:"), 0, 0)
        self.ocr_combo = QComboBox()
        self.ocr_combo.addItems(["paddle"])
        self.ocr_combo.setCurrentText(settings.OCR_TYPE)
        config_layout.addWidget(self.ocr_combo, 0, 1)
        
        config_layout.addWidget(QLabel("设备地址:"), 1, 0)
        self.device_label = QLabel(settings.DEVICE_URI)
        self.device_label.setStyleSheet("color: #666;")
        config_layout.addWidget(self.device_label, 1, 1)
        
        layout.addWidget(config_group)
        
        layout.addStretch()
        return panel
    
    def create_right_panel(self):
        """创建右侧日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 标题
        title = QLabel("执行日志")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #333; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #333333;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(self.clear_log_btn)
        
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self.save_log)
        log_control_layout.addWidget(self.save_log_btn)
        
        log_control_layout.addStretch()
        layout.addLayout(log_control_layout)
        
        return panel
    
    def setup_logging(self):
        """设置日志重定向"""
        # 不在这里重定向stdout，避免与TaskRunner冲突
        # self.log_handler = LogHandler(self.log_text)
        # import sys
        # sys.stdout = self.log_handler
        pass
    
    def start_tasks(self):
        """开始执行任务"""
        # 获取选中的任务
        selected_tasks = {}
        for key, (checkbox, func) in self.task_checkboxes.items():
            if checkbox.isChecked():
                selected_tasks[key] = func
        
        if not selected_tasks:
            QMessageBox.warning(self, "警告", "请至少选择一个任务！")
            return
        
        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(selected_tasks))
        self.progress_bar.setValue(0)
        
        # 启动任务线程
        self.task_runner = TaskRunner(selected_tasks)
        self.task_runner.log_signal.connect(self.append_log)
        self.task_runner.status_signal.connect(self.update_status)
        self.task_runner.progress_signal.connect(self.progress_bar.setValue)
        self.task_runner.finished_signal.connect(self.on_tasks_finished)
        self.task_runner.start()
    
    def stop_tasks(self):
        """停止执行任务"""
        if self.task_runner:
            self.task_runner.stop()
            self.task_runner.wait()
        
        self.on_tasks_finished()
    

    
    def on_tasks_finished(self):
        """任务完成回调"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.execute_queue_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")

    def add_selected_to_queue(self):
        """将选中的任务添加到队列"""
        selected_tasks = []
        for key, (checkbox, func) in self.task_checkboxes.items():
            if checkbox.isChecked():
                task_name = checkbox.text()
                selected_tasks.append((key, task_name, func))

        if not selected_tasks:
            QMessageBox.warning(self, "警告", "请至少选择一个任务！")
            return

        # 添加到队列
        for key, name, func in selected_tasks:
            if (key, name, func) not in self.task_queue:
                self.task_queue.append((key, name, func))

        self.update_queue_display()
        self.append_log(f"已添加 {len(selected_tasks)} 个任务到队列")

    def load_default_queue(self):
        """加载默认任务队列"""
        try:
            import yaml
            import os

            # 尝试从配置文件加载
            config_path = os.path.join(os.path.dirname(__file__), '..', 'default_queue.yaml')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    default_queue = [(item['key'], item['name']) for item in config.get('default_queue', [])]
            else:
                # 如果配置文件不存在，使用硬编码的默认队列
                default_queue = [
                    ("daily_rewards", "领取奖励"),
                    ("daily_share", "每日分享"),
                    ("task_guild", "任务集会所"),
                    ("adventure", "冒险副本"),
                    ("recruit", "招募"),
                    ("survival_challenge", "生存挑战"),
                    ("abundant_realm", "丰饶之间"),
                    ("organization_coin", "组织祈福"),
                    ("team_raid", "小队突袭"),
                    ("points_competition", "积分赛"),
                    ("equipment_upgrade", "升级装备"),
                ]
        except Exception as e:
            self.append_log(f"加载默认队列配置失败: {e}")
            # 使用硬编码的默认队列作为备选
            default_queue = [
                ("daily_rewards", "领取奖励"),
                ("daily_share", "每日分享"),
                ("task_guild", "任务集会所"),
                ("adventure", "冒险副本"),
                ("recruit", "招募"),
                ("survival_challenge", "生存挑战"),
                ("abundant_realm", "丰饶之间"),
                ("organization_coin", "组织祈福"),
                ("team_raid", "小队突袭"),
                ("duel_arena", "决斗场"),
                ("points_competition", "积分赛"),
                ("equipment_upgrade", "升级装备"),
            ]

        # 清空当前队列
        self.task_queue.clear()

        # 根据默认顺序添加任务
        for key, name in default_queue:
            if key in self.task_checkboxes:
                checkbox, func = self.task_checkboxes[key]
                self.task_queue.append((key, name, func))

        self.update_queue_display()
        self.append_log(f"已加载默认队列，共 {len(self.task_queue)} 个任务")

    def clear_queue(self):
        """清空任务队列"""
        self.task_queue.clear()
        self.update_queue_display()
        self.append_log("任务队列已清空")

    def save_current_queue_as_default(self):
        """保存当前队列为默认队列"""
        if not self.task_queue:
            QMessageBox.warning(self, "警告", "当前队列为空，无法保存！")
            return

        try:
            import yaml
            import os

            # 构建配置数据
            config_data = {
                'default_queue': [
                    {
                        'key': key,
                        'name': name,
                        'description': f"任务: {name}"
                    }
                    for key, name, func in self.task_queue
                ]
            }

            # 保存到配置文件
            config_path = os.path.join(os.path.dirname(__file__), '..', 'default_queue.yaml')

            # 如果文件存在，先读取现有配置，只更新default_queue部分
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = yaml.safe_load(f) or {}
                existing_config['default_queue'] = config_data['default_queue']
                config_data = existing_config

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)

            self.append_log(f"已保存当前队列为默认队列，共 {len(self.task_queue)} 个任务")
            QMessageBox.information(self, "成功", "当前队列已保存为默认队列！")

        except Exception as e:
            error_msg = f"保存默认队列失败: {e}"
            self.append_log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def update_queue_display(self):
        """更新队列显示"""
        # 保存当前选中的索引
        current_row = self.queue_list.currentRow()

        self.queue_list.clear()
        for i, (key, name, func) in enumerate(self.task_queue):
            item_text = f"{i+1}. {name}"
            self.queue_list.addItem(item_text)

        # 恢复选中状态
        if 0 <= current_row < len(self.task_queue):
            self.queue_list.setCurrentRow(current_row)

        # 更新按钮状态
        self.execute_queue_btn.setEnabled(len(self.task_queue) > 0)
        self._update_queue_operation_buttons()

    def execute_queue(self):
        """执行任务队列"""
        if not self.task_queue:
            QMessageBox.warning(self, "警告", "任务队列为空！")
            return

        if self.task_runner and self.task_runner.isRunning():
            QMessageBox.warning(self, "警告", "请先停止当前任务！")
            return

        # 构建任务字典
        queue_tasks = {}
        for key, name, func in self.task_queue:
            queue_tasks[name] = func

        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.execute_queue_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(queue_tasks))
        self.progress_bar.setValue(0)

        # 启动任务线程
        self.task_runner = TaskRunner(queue_tasks)
        self.task_runner.log_signal.connect(self.append_log)
        self.task_runner.status_signal.connect(self.update_status)
        self.task_runner.progress_signal.connect(self.progress_bar.setValue)
        self.task_runner.finished_signal.connect(self.on_tasks_finished)
        self.task_runner.start()

        self.append_log(f"开始执行队列任务，共 {len(queue_tasks)} 个任务")

    def move_task_up(self):
        """上移选中的任务"""
        current_row = self.queue_list.currentRow()
        if current_row <= 0 or current_row >= len(self.task_queue):
            return

        # 交换任务位置
        self.task_queue[current_row], self.task_queue[current_row - 1] = \
            self.task_queue[current_row - 1], self.task_queue[current_row]

        # 更新显示并保持选中状态
        self.update_queue_display()
        self.queue_list.setCurrentRow(current_row - 1)

        self.append_log(f"任务上移: {self.task_queue[current_row - 1][1]}")

    def move_task_down(self):
        """下移选中的任务"""
        current_row = self.queue_list.currentRow()
        if current_row < 0 or current_row >= len(self.task_queue) - 1:
            return

        # 交换任务位置
        self.task_queue[current_row], self.task_queue[current_row + 1] = \
            self.task_queue[current_row + 1], self.task_queue[current_row]

        # 更新显示并保持选中状态
        self.update_queue_display()
        self.queue_list.setCurrentRow(current_row + 1)

        self.append_log(f"任务下移: {self.task_queue[current_row + 1][1]}")

    def remove_selected_task(self):
        """删除选中的任务"""
        current_row = self.queue_list.currentRow()
        if current_row < 0 or current_row >= len(self.task_queue):
            return

        # 获取要删除的任务名称
        task_name = self.task_queue[current_row][1]

        # 删除任务
        del self.task_queue[current_row]

        # 更新显示
        self.update_queue_display()

        # 调整选中位置
        if current_row >= len(self.task_queue) and len(self.task_queue) > 0:
            self.queue_list.setCurrentRow(len(self.task_queue) - 1)
        elif len(self.task_queue) > 0:
            self.queue_list.setCurrentRow(current_row)

        self.append_log(f"已从队列删除任务: {task_name}")

    def _update_queue_operation_buttons(self):
        """更新队列操作按钮的状态"""
        current_row = self.queue_list.currentRow()
        queue_size = len(self.task_queue)

        # 上移按钮：不是第一个且有选中项
        self.move_up_btn.setEnabled(current_row > 0)

        # 下移按钮：不是最后一个且有选中项
        self.move_down_btn.setEnabled(0 <= current_row < queue_size - 1)

        # 删除按钮：有选中项
        self.remove_task_btn.setEnabled(0 <= current_row < queue_size)
    
    def append_log(self, text):
        """优化的日志添加方法"""
        if not text or not text.strip():
            return

        # 使用批量更新减少UI重绘
        current_time = time.time()
        if not hasattr(self, '_log_batch'):
            self._log_batch = []
            self._last_log_update = current_time

        self._log_batch.append(text.strip())

        # 每100ms或累积10条日志后批量更新
        if (current_time - self._last_log_update > 0.1) or len(self._log_batch) >= 10:
            self._flush_log_batch()

    def _flush_log_batch(self):
        """批量刷新日志"""
        if hasattr(self, '_log_batch') and self._log_batch:
            timestamp = datetime.now().strftime("%H:%M:%S")

            # 批量添加所有日志
            batch_text = '\n'.join([f"[{timestamp}] {log}" for log in self._log_batch])
            self.log_text.append(batch_text)

            # 清空批次并更新时间
            self._log_batch.clear()
            self._last_log_update = time.time()

            # 限制日志行数
            self._limit_log_lines()

    def _limit_log_lines(self, max_lines=1000):
        """限制日志显示行数，避免内存泄漏"""
        document = self.log_text.document()
        if document.blockCount() > max_lines:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            # 删除前面的行
            for _ in range(document.blockCount() - max_lines):
                cursor.select(cursor.SelectionType.BlockUnderCursor)
                cursor.removeSelectedText()
                cursor.deleteChar()
    
    def update_status(self, status=None):
        """更新状态"""
        if status is None:
            return  # 如果没有状态参数，直接返回
        
        self.status_label.setText(status)
        if "执行" in status:
            self.status_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        elif "完成" in status:
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        elif "失败" in status or "错误" in status:
            self.status_label.setStyleSheet("color: #F44336; font-weight: bold;")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_log(self):
        """保存日志"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志", f"naruto_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 
            "文本文件 (*.txt)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", f"日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存日志失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.task_runner and self.task_runner.isRunning():
            reply = QMessageBox.question(
                self, "确认退出", "任务正在执行中，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.stop_tasks()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("火影忍者自动化脚本")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 