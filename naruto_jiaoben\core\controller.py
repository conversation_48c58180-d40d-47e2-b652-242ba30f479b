import subprocess
import time
from . import settings

def _run_adb_command(command_parts):
    """
    内部函数，用于执行ADB命令。
    command_parts: 命令的参数列表，例如 ['shell', 'input', 'tap', '100', '200']
    """
    try:
        base_command = [settings.ADB_PATH, '-s', settings.DEVICE_URI]
        full_command = base_command + command_parts
        subprocess.run(full_command, shell=True, check=True, capture_output=True)
    except FileNotFoundError:
        raise FileNotFoundError(f"无法找到ADB可执行文件，请检查 config.yaml 中的 'adb_path': {settings.ADB_PATH}")
    except subprocess.CalledProcessError as e:
        error_message = e.stderr.decode('utf-8', errors='ignore').strip()
        raise ConnectionError(f"ADB命令执行失败: {' '.join(command_parts)}。错误信息: {error_message}")
    except Exception as e:
        raise RuntimeError(f"执行ADB命令时发生未知错误: {e}")

def connect():
    """
    测试设备连接是否正常

    :return: True if connected, False otherwise
    """
    try:
        _run_adb_command(['shell', 'echo', 'test'])
        return True
    except Exception:
        return False

def tap(x, y):
    """
    在模拟器指定坐标进行点击。

    :param x: x坐标
    :param y: y坐标
    """
    print(f"  - 点击坐标: ({x}, {y})")
    _run_adb_command(['shell', 'input', 'tap', str(x), str(y)])

def swipe(x1, y1, x2, y2, duration_ms=300):
    """
    在模拟器上进行滑动操作。

    :param x1: 起点x坐标
    :param y1: 起点y坐标
    :param x2: 终点x坐标
    :param y2: 终点y坐标
    :param duration_ms: 滑动持续时间（毫秒）
    """
    print(f"  - 滑动: 从 ({x1}, {y1}) 到 ({x2}, {y2})，耗时 {duration_ms}ms")
    _run_adb_command(['shell', 'input', 'swipe', str(x1), str(y1), str(x2), str(y2), str(duration_ms)])

def start_app(package_name, activity_name=None):
    """
    启动一个应用程序。

    :param package_name: 应用的包名
    :param activity_name: 应用的启动Activity名（可选）
    """
    print(f"  - 启动应用: {package_name}")
    if activity_name:
        component = f"{package_name}/{activity_name}"
        _run_adb_command(['shell', 'am', 'start', '-n', component])
    else:
        # 这种方式更通用，会启动应用的主/启动Activity
        _run_adb_command(['shell', 'monkey', '-p', package_name, '-c', 'android.intent.category.LAUNCHER', '1'])


def stop_app(package_name):
    """
    强制停止一个应用程序。

    :param package_name: 应用的包名
    """
    print(f"  - 停止应用: {package_name}")
    _run_adb_command(['shell', 'am', 'force-stop', package_name])


if __name__ == '__main__':
    # 这是一个简单的测试，当你直接运行此文件时会执行
    print("正在测试控制器功能...")
    try:
        # 测试前，请确保你的模拟器主屏幕上有一个可以点的图标
        print("将在 (500, 500) 位置点击一下，请确保该位置可点击。")
        time.sleep(2)
        tap(500, 500)
        print("点击测试完成。")

        print("将从 (800, 500) 滑动到 (200, 500)")
        time.sleep(2)
        swipe(800, 500, 200, 500)
        print("滑动测试完成。")
    except Exception as e:
        print(f"测试失败: {e}") 