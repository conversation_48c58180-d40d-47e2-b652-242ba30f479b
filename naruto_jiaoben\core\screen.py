import subprocess
import cv2
import numpy as np
import tempfile
import os
from . import settings

def get_screenshot():
    """
    优化的截图函数：减少IO操作和错误日志输出。
    采用两步法提高兼容性：先在设备内截图，再拉取到本地。
    """
    # 在设备上保存截图的临时路径
    device_tmp_path = "/sdcard/naruto_helper_screencap.png"

    # 使用 tempfile 模块在本地创建一个安全的临时文件
    # delete=False 允许我们在关闭文件句柄后仍然保留文件，以便adb可以写入
    lf, local_tmp_path = tempfile.mkstemp(suffix=".png")
    os.close(lf)

    try:
        # 步骤1: 在设备上截图 - 使用更快的方式
        _run_adb_command(['shell', 'screencap', '-p', device_tmp_path])

        # 步骤2: 将截图文件从设备拉取到本地临时文件
        _run_adb_command(['pull', device_tmp_path, local_tmp_path])

        # 使用OpenCV读取本地临时文件
        image_cv = cv2.imread(local_tmp_path, cv2.IMREAD_COLOR)
        if image_cv is None:
            raise IOError("成功拉取截图文件，但无法用OpenCV解码，文件可能已损坏。")

        return image_cv

    except Exception as e:
        # 减少错误日志输出，避免UI卡顿
        # 只在调试模式下输出详细错误信息
        if hasattr(settings, 'DEBUG') and settings.DEBUG:
            import traceback
            print("截图过程中发生错误:")
            traceback.print_exc()
        # 重新抛出异常，让上层知道任务失败
        raise RuntimeError(f"获取截图失败: {e}")
    finally:
        # 步骤3: 清理设备上的临时文件（静默处理）
        try:
            _run_adb_command(['shell', 'rm', device_tmp_path])
        except Exception:
            # 静默处理删除失败，避免不必要的日志输出
            pass

        # 清理本地的临时文件
        if os.path.exists(local_tmp_path):
            os.remove(local_tmp_path)


def _run_adb_command(command_parts):
    """
    内部辅助函数，用于执行ADB命令。
    """
    base_command = [settings.ADB_PATH, '-s', settings.DEVICE_URI]
    full_command = base_command + command_parts
    subprocess.run(full_command, capture_output=True, check=True)


if __name__ == '__main__':
    # 这是一个简单的测试，当你直接运行此文件时会执行
    # 它会截取一张图，显示出来，然后保存为 test_screenshot.png
    print("正在测试截图功能...")
    try:
        screenshot = get_screenshot()
        print("截图成功！")
        cv2.imshow("Test Screenshot", screenshot)
        print("按任意键关闭窗口并保存图片...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        cv2.imwrite("test_screenshot.png", screenshot)
        print("测试图片已保存为 test_screenshot.png")
    except Exception as e:
        print(f"测试失败: {e}") 