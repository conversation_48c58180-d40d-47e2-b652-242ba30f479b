import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_abundant_realm(fast_click=True, click_interval=0.1, round_interval=0.2):
    """
    执行丰饶之间任务

    :param fast_click: 是否使用快速点击模式
    :param click_interval: 每次点击之间的间隔（秒）
    :param round_interval: 每轮点击之间的间隔（秒）
    """
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行丰饶之间任务...")
        
        # 使用通用滑动查找函数查找丰饶之间功能
        if not utils.swipe_and_find_template("tpl1744703193584.png"):
            print("  -> [错误] 未找到丰饶之间功能")
            return False
        
        # 等待丰饶之间界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744703384080.png")
        print("  -> 丰饶之间界面已加载")
        
        # 点击进入按钮
        utils.wait_and_tap_template("tpl1744703419752.png")
        print("  -> 已点击进入按钮")
        
        # 等待战斗界面加载（只等待，不点击）
        time.sleep(15)
        print("  -> 战斗界面已加载")
        time.sleep(5)
        # 执行自动点击序列
        coordinates = [
            (718, 144), (770, 149), (837, 165), 
            (717, 209), (773, 199), (767, 267),
            (830, 246), (1012, 495)
        ]
        
        if fast_click:
            print(f"  -> 开始快速自动点击序列（间隔: {click_interval}s）...")
            for round_num in range(50):
                if round_num % 5 == 0:  # 每5轮显示一次进度
                    print(f"  -> 执行第 {round_num + 1}/30 轮快速点击")
                for pos in coordinates:
                    controller.tap(pos[0], pos[1])
                    time.sleep(click_interval)
                # 每轮之间稍微停顿，避免过于频繁
                time.sleep(round_interval)
            print("  -> 快速自动点击序列完成")
        else:
            print("  -> 开始标准自动点击序列...")
            for round_num in range(30):
                print(f"  -> 执行第 {round_num + 1}/30 轮点击")
                for pos in coordinates:
                    controller.tap(pos[0], pos[1])
                    time.sleep(0.8)  # 标准间隔
            print("  -> 标准自动点击序列完成")
        
        time.sleep(20)  # 等待战斗完成
        
        # 等待战斗完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744704170936.png")
        print("  -> 战斗完成界面已加载")
        
        # 点击完成按钮
        utils.wait_and_tap_template("tpl1744704197617.png")
        print("  -> 已点击完成按钮")
        
        print("  -> 丰饶之间任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc()

# 向后兼容的包装函数
def run():
    """标准的run函数，使用快速点击模式"""
    return run_abundant_realm(fast_click=True, click_interval=0.1, round_interval=0.2)

def run_fast():
    """超快速点击模式"""
    return run_abundant_realm(fast_click=True, click_interval=0.05, round_interval=0.1)

def run_standard():
    """标准点击模式（兼容旧版本）"""
    return run_abundant_realm(fast_click=False)