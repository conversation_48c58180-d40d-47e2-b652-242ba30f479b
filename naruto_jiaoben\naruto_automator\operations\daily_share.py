import time
from naruto_automator import utils
from core import controller, screen, vision
from naruto_automator.states import state_checker

def run_daily_share():
    """执行每日分享和领取体力任务"""
    if not state_checker.is_in_main_menu():
        print("  -> [警告] 任务开始时不在主菜单，尝试启动恢复程序...")
        if not utils.handle_timeout_and_return_to_main():
            print("  -> [错误] 恢复失败，无法回到主菜单。任务中断。")
            return # 恢复失败，则彻底中断任务
    try:
        print("  -> 开始执行每日分享和领取体力任务...")
        
        # 使用通用滑动查找函数查找每日分享功能
        if not utils.swipe_and_find_template("tpl1744706665297.png"):
            print("  -> [错误] 未找到每日分享功能")
            return False
        
        # 等待每日分享界面加载（只等待，不点击）
        utils.wait_for_template("tpl1744706698571.png")
        print("  -> 每日分享界面已加载")
        
        # 点击分享按钮
        utils.wait_and_tap_template("tpl1744706712729.png")
        print("  -> 已点击分享按钮")
        
        # 等待分享确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744706728434.png")
        print("  -> 分享确认界面已加载")
        
        # 点击确认分享
        utils.wait_and_tap_template("tpl1744706750233.png")
        print("  -> 已确认分享")
        
        time.sleep(10.0)  # 等待分享完成
        
        # 等待分享完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744706784977.png")
        print("  -> 分享完成界面已加载")
        
        # 点击返回按钮
        utils.wait_and_tap_template("tpl1744706810545.png")
        print("  -> 已点击返回按钮")
        
        time.sleep(30.0)  # 等待返回动画
        
      
        utils.wait_for_template("tpl1744706840257.png")
        print("  -> 返回界面已加载")
        
        # 点击领取体力
        utils.wait_and_tap_template("tpl1744706870780.png")
        print("  -> 退回主界面")
        
        # 等待领取确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744706931346.png")
        print("  -> 界面已加载")
        
        # 点击确认领取
        utils.wait_and_tap_template("tpl1744706911970.png")
        print("  -> 已确认领取体力")
        
        # 等待领取完成界面（只等待，不点击）
        utils.wait_for_template("tpl1744706978078.png")
        print("  -> 领取完成界面已加载")
        
        # 点击确认完成
        utils.wait_and_tap_template("tpl1744706989577.png")
        print("  -> 已确认领取完成")
        
        # 等待最终确认界面（只等待，不点击）
        utils.wait_for_template("tpl1744707023266.png")
        print("  -> 最终确认界面已加载")
        
        # 点击最终确认
        utils.wait_and_tap_template("tpl1744707039969.png")
        print("  -> 已点击最终确认")
        
        # 点击关闭按钮
        utils.wait_and_tap_template("tpl1744707069611.png")
        print("  -> 已点击关闭按钮")
        
        # 等待返回主界面（只等待，不点击）
        utils.wait_for_template("tpl1744707087265.png")
        print("  -> 返回主界面")
        
        # 点击返回主界面
        utils.wait_and_tap_template("tpl1744707116785.png")
        print("  -> 已返回主界面")
        
        print("  -> 每日分享和领取体力任务完成")
        
        # 任务结束后：再次检查是否已成功返回主菜单
        if state_checker.is_in_main_menu():
            print("  -> 确认已成功返回主菜单。")
        else:
            print("  -> [警告] 任务结束后未能返回主菜单，尝试启动恢复程序...")
            utils.handle_timeout_and_return_to_main()
        
        return True
        
    except TimeoutError as e:
        print(f"  -> [错误] 任务执行失败，因为等待图片超时: {e}")
        # 调用通用的错误恢复函数，尝试返回主菜单
        utils.handle_timeout_and_return_to_main()
    except Exception as e:
        import traceback
        print(f"  -> [错误] 发生未知错误: {e}")
        traceback.print_exc() 