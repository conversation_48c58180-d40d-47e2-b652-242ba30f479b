# 火影忍者脚本项目结构

## 📁 项目目录结构

```
naruto_jiaoben/
├── main.py                           # 主程序入口
├── run_ui.py                         # UI界面启动器
├── 原始.py                           # 原始脚本备份
├── config.yaml                       # 主配置文件
├── default_queue.yaml               # 默认任务队列配置
├── cleanup.py                       # 项目清理工具
├── mumu周胜9.10最新.mmor            # MuMu宏录制文件
├── 
├── core/                            # 核心功能模块
│   ├── __init__.py
│   ├── controller.py                # 设备控制（ADB操作）
│   ├── screen.py                    # 截图功能
│   ├── vision.py                    # 图像识别
│   ├── ocr_system.py               # OCR文字识别
│   └── settings.py                 # 设置管理
├── 
├── naruto_automator/               # 自动化功能模块
│   ├── __init__.py
│   ├── utils.py                    # 工具函数
│   ├── operations/                 # 操作模块
│   │   ├── __init__.py
│   │   ├── duel_arena.py          # 决斗场
│   │   ├── points_competition.py  # 积分赛
│   │   ├── survival_challenge.py  # 生存挑战
│   │   ├── daily_tasks.py         # 日常任务
│   │   └── ...
│   └── states/                     # 状态检测
│       ├── __init__.py
│       └── state_checker.py       # 游戏状态检测
├── 
├── ui/                             # 用户界面
│   ├── __init__.py
│   └── main_window.py             # 主窗口界面
├── 
├── assets/                         # 图片资源
│   ├── victory_1.png              # 胜利图片1
│   ├── victory_2.png              # 胜利图片2
│   ├── defeat_1.png               # 失败图片1
│   └── tpl*.png                   # 各种模板图片
├── 
├── 🆕 MuMu宏控制相关文件
├── mumu_record_window_controller.py # MuMu操作录制窗口控制器
├── duel_arena_mumu_macro.py        # 使用MuMu宏的决斗场版本
├── 
└── 配置和依赖文件
    ├── requirements.txt            # Python依赖
    ├── requirements_ocr.txt        # OCR相关依赖
    ├── requirements_ui.txt         # UI相关依赖
    ├── environment.yml             # Conda环境配置
    └── start_ui_conda.bat         # Conda启动脚本
```

## 🎯 核心文件说明

### **主要程序**
- `main.py` - 命令行版本主程序
- `run_ui.py` - 图形界面版本
- `原始.py` - 原始脚本备份

### **MuMu宏控制**
- `mumu_record_window_controller.py` - **核心**：控制MuMu操作录制窗口
- `duel_arena_mumu_macro.py` - **决斗场**：使用真实宏录制的版本
- `mumu周胜9.10最新.mmor` - **宏文件**：您录制的操作序列

### **核心模块**
- `core/controller.py` - ADB设备控制
- `core/vision.py` - 图像识别和模板匹配
- `core/screen.py` - 截图功能
- `core/ocr_system.py` - OCR文字识别

### **自动化功能**
- `naruto_automator/operations/` - 各种游戏操作
- `naruto_automator/states/` - 游戏状态检测
- `naruto_automator/utils.py` - 通用工具函数

## 🚀 使用方法

### **方法1：使用MuMu宏（推荐）**
```bash
# 1. 打开MuMu模拟器
# 2. 启动火影忍者游戏
# 3. 按快捷键打开"MuMu 操作录制"窗口
# 4. 运行决斗场宏版本
python duel_arena_mumu_macro.py
```

### **方法2：使用图形界面**
```bash
python run_ui.py
```

### **方法3：使用命令行**
```bash
python main.py
```

## 🧹 项目清理

已删除的测试文件：
- ❌ 所有 `test_*.py` 测试文件
- ❌ 所有 `*.md` 文档文件（除本文件）
- ❌ 无用的宏测试文件
- ❌ OCR示例文件
- ❌ 日志文件

保留的核心文件：
- ✅ 主程序和UI
- ✅ 核心功能模块
- ✅ MuMu宏控制器
- ✅ 配置和依赖文件
- ✅ 图片资源

## 📝 下一步

现在项目结构清晰，可以：
1. **测试MuMu宏控制器**
2. **完善决斗场功能**
3. **添加其他游戏功能**
4. **优化用户界面**
