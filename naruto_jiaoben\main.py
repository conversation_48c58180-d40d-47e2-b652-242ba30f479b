import time
import subprocess
import yaml
import sys
import os
from core import settings
from core.ocr_system import init_ocr_system, get_ocr_system
from naruto_automator.operations import adventure, recruit, task_guild, daily_share, equipment_upgrade, survival_challenge, abundant_realm, organization_coin, team_raid, points_competition, daily_rewards
from core import controller, screen
# from naruto_automator.operations import recruit # 当你创建 recruit.py 后，像这样导入它

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def _connect_device():
    """
    确保设备已连接。
    """
    print(f"  -> 尝试连接设备: {settings.DEVICE_URI}...")
    try:
        # 使用 subprocess 模块来执行 adb connect 命令
        command = [settings.ADB_PATH, 'connect', settings.DEVICE_URI]
        # 移除 shell=True，让命令执行更可靠
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        if "connected" in result.stdout or "already connected" in result.stdout:
            print(f"  -> 设备连接成功: {settings.DEVICE_URI}")
            return True
        else:
            print(f"  -> 设备连接失败，响应信息: {result.stdout.strip()}")
            return False
    except Exception as e:
        print(f"  -> [错误] 执行 ADB connect 命令时出错: {e}")
        return False

def _init_ocr_system():
    """
    初始化OCR系统
    """
    if settings.OCR_AUTO_INIT:
        print(f"  -> 初始化OCR系统 ({settings.OCR_TYPE})...")
        if init_ocr_system(settings.OCR_TYPE):
            ocr_system = get_ocr_system(settings.OCR_TYPE)
            status = ocr_system.get_ocr_status()
            print(f"  -> OCR系统初始化成功: {status}")
            return True
        else:
            print("  -> [警告] OCR系统初始化失败，某些功能可能无法正常工作")
            return False
    else:
        print("  -> 跳过OCR系统初始化")
        return True

def main():
    """
    程序主入口。
    修改为单次执行模式，为将来集成UI做准备。
    """
    print("="*20)
    print("  火影忍者自动化脚本启动")
    print("="*20)
    
    # 配置已经在 settings.py 中加载，无需重复加载
    print("配置加载成功")
    
    # 初始化OCR系统
    _init_ocr_system()
    
    # 自动连接设备
    if not _connect_device():
        print("\n无法连接到指定的模拟器，请检查模拟器是否已启动，以及config.yaml中的device_uri是否正确。")
        return # 连接失败则直接退出程序

    print(f"将根据 config.yaml 中的任务设置执行...")
    print(f"任务: {[task for task, config in settings.TASKS_CONFIG.items() if config.get('enabled')]}")
    print("\n3秒后开始，请确保模拟器在前台...")
    time.sleep(3)

    try:
        # --- 开始执行任务 ---
        print("\n开始执行任务...")

        # 示例：执行冒险副本任务
        if settings.TASKS_CONFIG.get('adventure', {}).get('enabled'):
            print("\n[任务] -> 开始执行'冒险副本'")
            adventure.run() # 调用 '冒险副本' 的操作函数

        # 示例：执行招募任务
        if settings.TASKS_CONFIG.get('recruit', {}).get('enabled'):
            print("\n[任务] -> 开始执行'招募'")
            recruit.run_recruit()

        # 执行升级装备任务
        if settings.TASKS_CONFIG.get('equipment_upgrade', {}).get('enabled'):
            print("\n[任务] -> 开始执行'升级装备'")
            equipment_upgrade.run()

        # 执行任务集会所任务
        if settings.TASKS_CONFIG.get('task_guild', {}).get('enabled'):
            print("\n[任务] -> 开始执行'任务集会所'")
            task_guild.run_task_guild()

        # 执行每日分享任务
        if settings.TASKS_CONFIG.get('daily_share', {}).get('enabled'):
            print("\n[任务] -> 开始执行'每日分享'")
            daily_share.run_daily_share()

        # 执行生存挑战任务
        if settings.TASKS_CONFIG.get('survival_challenge', {}).get('enabled'):
            print("\n[任务] -> 开始执行'生存挑战'")
            survival_challenge.run_survival_challenge()

        # 执行丰饶之间任务
        if settings.TASKS_CONFIG.get('abundant_realm', {}).get('enabled'):
            print("\n[任务] -> 开始执行'丰饶之间'")
            abundant_realm.run_abundant_realm()

        # 执行组织祈福和铜币收集任务
        if settings.TASKS_CONFIG.get('organization_coin', {}).get('enabled'):
            print("\n[任务] -> 开始执行'组织祈福和铜币收集'")
            organization_coin.run_organization_coin()

        # 执行小队突袭任务
        if settings.TASKS_CONFIG.get('team_raid', {}).get('enabled'):
            print("\n[任务] -> 开始执行'小队突袭'")
            team_raid.run_team_raid()

        # 执行积分赛任务
        if settings.TASKS_CONFIG.get('points_competition', {}).get('enabled'):
            print("\n[任务] -> 开始执行'积分赛'")
            points_competition.run_points_competition()

        # 执行领取奖励任务
        if settings.TASKS_CONFIG.get('daily_rewards', {}).get('enabled'):
            print("\n[任务] -> 开始执行'领取奖励'")
            daily_rewards.run()

        # ... 在这里添加其他任务的判断和调用 ...

    except KeyboardInterrupt:
        print("\n程序被用户手动中断。")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        # 可以加入traceback打印更详细的错误信息
        import traceback
        traceback.print_exc()
    finally:
        print("\n====================")
        print("所有任务执行完毕。")
        print("====================")


if __name__ == '__main__':
    main() 